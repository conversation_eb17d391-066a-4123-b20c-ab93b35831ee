
/* Reset default browser styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height:100%;
}

body {
  margin:0;
}

.bg {
  animation:slide 8s ease-in-out infinite alternate;
  background-image: linear-gradient(-60deg, #6c3 50%, #09f 50%);
  bottom:0;
  left:-50%;
  opacity:.5;
  position:fixed;
  right:-50%;
  top:0;
  z-index:-1;
}

.bg2 {
  animation-direction:alternate-reverse;
  animation-duration:10s;
}

.bg3 {
  animation-duration:13s;
}

/* .content {
  background-color:rgba(255,255,255,.8);
  border-radius:.25em;
  box-shadow:0 0 .25em rgba(0,0,0,.25);
  box-sizing:border-box;
  left:50%;
  padding:10vmin;
  position:fixed;
  text-align:center;
  top:50%;
  transform:translate(-50%, -50%);
} */

h1 {
  font-family:monospace;
}

@keyframes slide {
  0% {
    transform:translateX(-25%);
  }
  100% {
    transform:translateX(25%);
  }
}

.container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  background-color: rgba(255, 255, 255, 0.7);
  flex: 0 0 200px;
  margin-left: -10px;
  padding: 20px;
  transition: all 4s ease-in-out;
  border-radius: 20px;
}

.sidebar.hidden {
  display: none;
}

.sidebar-container.hidden {
  width: 0;
  flex: 0;
  padding: 0;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sidebar-toggle {
  background-color: #444;
  color: #fff;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
}

.sidebar-menu li {
  margin-bottom: 10px;
}

.sidebar-menu li a {
  color: #333;
  text-decoration: none;
}



.main-content {
  flex: 1;
  padding: 20px;
  transition: all 0.3s ease-in-out;
  margin-left: 0px;
  border-radius: 20px;
}

.sidebar.hidden + .main-content {
  margin-left: 0;
}


.content-header {
  margin-bottom: 10px;
}


.content {
  background-color: rgba(255, 255, 255, 0.7);
  height: calc(100% - 40px);
  padding: 20px;
  border-radius: 20px;
}

.modal-buttons {
  background-color: rgba(255, 255, 255, 0.7);
  flex: 0 0 200px;
  padding: 20px;
  border-radius: 20px;
}

.modal-buttons h3 {
  margin-bottom: 10px;
}

/* Modal CSS */
.modal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #fff;
  margin: 20% auto;
  padding: 20px;
  border: 1px solid #888;
  width: 400px;
  max-width: 90%;
  position: relative;
  border-radius: 20px;
}

.modal-content h2 {
  margin-top: 0;
}

.modal-content label {
  display: block;
  margin-top: 10px;
}

.modal-content input[type="text"],
.modal-content textarea,
.modal-content select {
  width: 100%;
  padding: 8px;
  border-radius: 5px;
  border: 1px solid #ccc;
  box-sizing: border-box;
}

.modal-content button[type="submit"] {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: #4caf50;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.modal-content .close {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #888;
  cursor: pointer;
}

.modal-content .close:hover,
.modal-content .close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

/* .dummy-button {
  display: block;
  margin-bottom: 10px;
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  background-color: #333;
  color: #fff;
  cursor: pointer;
  text-align: center;
} */

/* Add the glassmorphic effect styles */
/* You can use a CSS library or custom styles to achieve this effect */

