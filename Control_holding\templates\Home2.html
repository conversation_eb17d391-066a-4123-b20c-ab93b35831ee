<!-- <!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Management Platform</title>
    <script src="https://code.jquery.com/jquery-1.10.2.js"></script>
    <link rel="stylesheet" href="/static/assets/control_2024.css">
       
    <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
</head>
<body>
    <div id="container" >
        <div id="pivot">
            <div id="revolvingDoor">
             
                <div class="pane" id="pane1">Page 1</div>
                <div class="pane" id="pane2">Page 2</div>
                <div class="pane" id="pane3">Page 3</div>
                <div class="pane" id="pane4">Page 4</div>
                <div class="pane" id="pane5">Page 5</div>
            </div>
        </div>
    </div>
    <script src="/static/assets/control_2024.js" ></script>
</body>
</html> -->


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Interactive Doors</title>
        <style>
/* style.css */
body, html {
  height: 100%;
  margin: 0;
}

#doorContainer {
  width: 100%;
  height: 100%;
  position: relative;
  perspective: 1000px; /* for 3D effects */
}

.doorPanel {
  width: 100px;
  height: 200px;
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5); /* glassified effect */
  border: 2px solid #000;
  transition: transform 1s;
}

/* Initial revolving door style - will be modified by JavaScript */


        </style>
    </head>
    <body>
        <div id="doorContainer">
            <!-- Example of three door panels -->
            <div class="doorPanel" data-destination="page1.html"></div>
            <div class="doorPanel" data-destination="page2.html"></div>
            <div class="doorPanel" data-destination="page3.html"></div>
        </div>
        <script>
// script.js
document.addEventListener("DOMContentLoaded", function() {
    const panels = document.querySelectorAll('.doorPanel');
    const angleStep = 360 / panels.length;

    panels.forEach((panel, index) => {
        panel.style.transform = `rotateY(${angleStep * index}deg) translateZ(250px)`;
    });
});


// script.js - continued
function transitionToCarousel() {
    const panels = document.querySelectorAll('.doorPanel');
    const angleStep = 360 / panels.length;
    const carouselRadius = 250; // adjust as needed

    panels.forEach((panel, index) => {
        // Adjust the transform to create a carousel layout
        const rotateY = angleStep * index;
        const zIndex = index === 0 ? 1000 : 1000 - index; // bring the first panel to front
        panel.style.transform = `rotateY(${rotateY}deg) translateZ(${carouselRadius}px)`;
        panel.style.zIndex = zIndex;
    });
}


document.addEventListener('keydown', function(event) {
    if (event.key === 'Enter') {
        transitionToCarousel();
    }
});

document.getElementById('doorContainer').addEventListener('click', function() {
    transitionToCarousel();
});

// script.js - continued
// script.js - continued
function expandPanel(panel) {
    // Use GSAP or similar library for more complex animations
    panel.style.transition = 'transform 1s, width 1s, height 1s';
    panel.style.transform = 'translate(-50%, -50%) scale(10)';
    panel.style.width = '100%';
    panel.style.height = '100%';
    panel.style.left = '50%';
    panel.style.top = '50%';
    panel.style.position = 'fixed';

    // Optionally, load content or redirect
    // window.location.href = panel.getAttribute('data-destination');
}


document.querySelectorAll('.doorPanel').forEach(panel => {
    panel.addEventListener('click', function() {
        // Highlight the clicked panel, and move it slightly forward
        this.style.transform += ' scale(1.1)';
        expandPanel(this);
    });
});


        </script>
    </body>
    </html>
    

</body>
</html>