<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400,300' rel='stylesheet' type='text/css'>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .login-container {
            background-color: #fff;
            padding: 2rem;
            border: 2px solid #000;
            width: 300px;
            text-align: center;
        }

        .login-container h2 {
            margin-bottom: 1rem;
        }

        .login-container form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .login-container label {
            font-weight: bold;
        }

        .login-container input {
            padding: 0.5rem;
            border: 2px solid #000;
            font-size: 1rem;
        }

        .login-container button {
            background-color: #000;
            color: #fff;
            border: none;
            padding: 1rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .login-container button:hover {
            background-color: #333;
        }

        .login-container .error {
            color: white;
            background-color: black;
            margin-top: 1rem;
            padding: 0.5rem;
            font-weight: bold;
            border: 2px solid red;
            text-transform: uppercase;
        }

        .login-container input.error-field {
            border: 2px solid red;
            background-color: #fff0f0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>Login</h2>
        <form id="login-form" method="POST" action="/">
            <label for="staff_id">Staff ID:</label>
            <input type="text" id="staff_id" name="staff_id" {% if error_type == 'staff_id' %}class="error-field"{% endif %}>
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" {% if error_type == 'password' %}class="error-field"{% endif %}>
            <button type="submit">Login</button>
        </form>
        {% if error %}
        <div class="error" id="error-message">{{ error }}</div>
        {% endif %}
    </div>
</body>
</html>
