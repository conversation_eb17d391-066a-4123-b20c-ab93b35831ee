<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR Administration Dashboard</title>

    <!-- jQuery CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Google Fonts -->
    <link href='http://fonts.googleapis.com/css?family=Open+Sans:400,300' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Raleway' rel='stylesheet' type='text/css'>

    <!-- <link rel="stylesheet" href="/static/assets/hr_page.css"> -->
    <link rel="stylesheet" type="text/css" href="/static/assets/cal/dist/calendar-heatmap.min.css">

<style>
    body {
    font-family: 'Open Sans', sans-serif;
    background-color: #f5f5f5;
    color: #333;
    margin: 0;
    padding: 0;
}

header {
    background-color: #000;
    color: #fff;
    padding: 1rem;
    text-align: center;
    font-size: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#navigation {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

#navigation a {
    color: #fff;
    text-decoration: none;
    font-size: 1rem;
}

main {
    padding: 2rem;
}

#button-row {
    display: flex;
    justify-content: space-around;
    margin-bottom: 2rem;
}

button {
    background-color: #000;
    color: #fff;
    border: none;
    padding: 1rem 2rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #333;
}

.section-content {
    background-color: #fff;
    padding: 2rem;
    border: 2px solid #000;
    margin-bottom: 2rem;
}

form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

label {
    font-weight: bold;
}

input, select, textarea {
    padding: 0.5rem;
    border: 2px solid #000;
    font-size: 1rem;
}

hr {
    border: 2px solid #000;
    margin: 2rem 0;
}

/* New styles for the two-column layout */
#employee-skills-chart {
    display: flex;
    align-items: flex-start;
}

.radar-container {
    flex: 3; /* Larger column for the graph */
    margin-right: 20px; /* Space between the columns */
}

.radar-tooltip-container {
    flex: 1; /* Smaller column for the tooltip */
    background-color: #f9f9f9;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.radar-tooltip-content p {
    margin: 5px 0;
}

/* Styles for the new task assignment tab */
#task-assignment-tabs {
    display: flex;
    justify-content: space-around;
    margin-bottom: 2rem;
}

#task-assignment-tabs button {
    background-color: #000;
    color: #fff;
    border: none;
    padding: 1rem 2rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

#task-assignment-tabs button:hover {
    background-color: #333;
}

.task-content {
    display: none;
}

.task-content.active {
    display: block;
}

.recommendations {
    margin-top: 2rem;
}

.recommendations h3 {
    margin-bottom: 1rem;
}

.recommendations ul {
    list-style-type: none;
    padding: 0;
}

.recommendations li {
    margin-bottom: 1rem;
}

.recommendations li input[type="radio"] {
    margin-right: 1rem;
}

/* Styles for KPA section */
.kpa-section {
    margin-top: 2rem;
}

.kpa-section h3 {
    margin-bottom: 1rem;
}

.kpa-input-group {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.kpa-input-group input[type="text"] {
    flex: 1;
    margin-right: 1rem;
}

.kpa-input-group input[type="date"] {
    flex: 1;
    margin-right: 1rem;
}

.kpa-input-group button {
    background-color: #fff;
    border: 2px solid #000;
    color: #000;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.kpa-input-group button:hover {
    background-color: #333;
    color: #fff;
}

/* Styles for notifications */
.notifications-container {
    position: relative;
}

.notifications-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 2px solid #000;
    width: 300px;
    z-index: 1000;
}

.notifications-dropdown.active {
    display: block;
}

.notification {
    border: 2px solid #000;
    padding: 1rem;
    margin: 1rem 0;
    background-color: #fff;
    cursor: pointer;
    transition: background-color 0.3s;
}

.notification:hover {
    background-color: #e9e9e9;
}

.notification p {
    margin: 0;
    font-weight: bold;
}

.notification small {
    color: #666;
}

.user-logo {
    margin-left: 1rem;
}

.user-logo img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}



/* Styles for calendar heatmap modal */
.heatmap-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.heatmap-modal.active {
    display: flex;
}

.heatmap-content {
    background-color: #fff;
    padding: 2rem;
    border: 2px solid #000;
    width: 80%;
    height: 80%;
    overflow: auto;
}

.heatmap-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: #000;
    color: #fff;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
}

.heatmap-close:hover {
    background-color: #333;
}

.task-progress-item {
    border: 1px solid #ddd;
    padding: 15px;
    margin: 10px 0;
    border-radius: 8px;
    cursor: pointer;
}

.task-progress-item:hover {
    background-color: #e9e9e9;
}

.task-progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.kpa-item {
    padding: 8px;
    margin: 5px 0;
    border-left: 4px solid;
}

.kpa-item.not-started { border-color: #ccc; }
.kpa-item.under-review { border-color: #ffeb3b; }
.kpa-item.approved { border-color: #4caf50; }
.kpa-item.revisions-requested { border-color: #f44336; }

.draft-info {
    font-size: 0.9em;
    color: #666;
    margin-top: 4px;
}

.task-status {
    font-weight: bold;
    text-transform: capitalize;
}


/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: #fff;
    padding: 2rem;
    border: 2px solid #000;
    width: 80%;
    max-width: 800px;
    overflow: auto;
    position: relative;
}

.close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: #000;
    color: #fff;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
}

.close:hover {
    background-color: #333;
}

.draft-item {
    border-bottom: 2px solid #000;
    padding: 1rem;
    margin: 1rem 0;
}

.draft-review {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f9f9f9;
    border: 2px solid #000;
}

/* Error handling styles */
.error {
    color: white;
    background-color: black;
    margin-top: 1rem;
    padding: 0.5rem;
    font-weight: bold;
    border: 2px solid red;
    text-transform: uppercase;
}

input.error-field, select.error-field, textarea.error-field {
    border: 2px solid red;
    background-color: #fff0f0;
}


/* Styles for task tracking */
#progress {
  position: relative;
  margin-bottom: 30px;
  border: 2px solid #000;
  padding: 1rem;
  background-color: #fff;
  border-radius: 8px;
}

#progress-bar {
  position: absolute;
  background: #4584ff;
  height: 10px;
  width: 0%;
  top: 50%;
  left: 0;
  border-radius: 5px;
}

#progress-text {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

#progress-text::before {
  content: "";
  background-color: lightgray;
  position: absolute;
  top: 50%;
  left: 0;
  height: 10px;
  width: 100%;
  z-index: -1;
  border-radius: 5px;
}

#progress-text .step {
  border: 3px solid lightgray;
  border-radius: 8px;
  width: 100px;
  height: 50px;
  text-align: center;
  background-color: #fff;
  font-family: sans-serif;
  font-size: 14px;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
  color: #000;
}

#progress-text .step label {
  margin-top: 10px;
  font-size: 0.7rem;
  font-weight: bold;
  color: inherit;
  word-wrap: break-word;
  white-space: normal;
}

#progress-text .step.completed {
  border-color: #4caf50;
}

#progress-text .step.revisions-needed {
  border-color: #ffeb3b;
}

#progress-text .step.under-review {
  border-color: #ff9800;
}

#progress-text .step.in-progress {
  border-color: #2196f3;
}

#progress-text .step.active::after {
  content: "✓";
  color: #fff;
  z-index: 2;
  position: absolute;
  top: 2px;
  left: 8px;
  font-size: 12px;
  font-weight: bold;
}

#progress-text .step.progress {
  border-color: #4584ff;
}

#progress-text .step.progress::after {
  content: "•";
  transform: scale(3);
  position: absolute;
  left: 10px;
  top: 1.5px;
  color: #4584ff;
}

#progress-text .step.progress label {
  color: #4584ff;
}

.btn {
  background: lightgray;
  border: none;
  border-radius: 3px;
  padding: 6px 12px;
}

.past-date {
  background-color: #f44336;
  color: #fff;
}

.future-date {
  background-color: #4caf50;
  color: #fff;
}



.tasks-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.tasks-table th, .tasks-table td {
    border: 2px solid #000;
    padding: 0.5rem;
    text-align: left;
}

.tasks-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.view-task-btn {
    background-color: #000;
    color: #fff;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
}

.view-task-btn:hover {
    background-color: #333;
}

.approval-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.approval-buttons button {
    flex: 1;
    padding: 1rem;
    font-size: 1rem;
    cursor: pointer;
    border: 2px solid #000;
}

#approve-task-btn {
    background-color: #fff;
    color: #000;
}

#approve-task-btn:hover, #approve-task-btn.active {
    background-color: #4caf50;
    color: #fff;
}

#reject-task-btn {
    background-color: #fff;
    color: #000;
}

#reject-task-btn:hover, #reject-task-btn.active {
    background-color: #f44336;
    color: #fff;
}

/* D3 Tree Styles */
.node circle {
    fill: #fff;
    stroke: steelblue;
    stroke-width: 3px;
    cursor: pointer;
}

.node text {
    font: 12px sans-serif;
    cursor: pointer;
    font-weight: bold;
}

.link {
    fill: none;
    stroke: #ccc;
    stroke-width: 2px;
}

.node:hover circle {
    fill: #f8f8f8;
}


</style>

</head>
<body>
<header>
    <div id="logo">HR Logo</div>
    <nav id="navigation">
        <!-- Navigation icons -->
    </nav>
    <div class="user-logo">
        <img src="path/to/user/logo.jpg" alt="User Logo">
    </div>
    <div class="notifications-container">
        <button id="notifications-btn">Notifications</button>
        <div id="notifications-dropdown" class="notifications-dropdown">
            <div id="notifications-content">
                <!-- Notifications will be dynamically inserted here -->
            </div>
        </div>
    </div>
</header>

<main>
    <section id="button-row">
        <!-- Buttons to load different content -->
        <button data-section="user-profile">User Profile</button>
        <button data-section="organisational-objectives">Organisational Objectives</button>
        <button data-section="objectives-tree">Objectives Tree</button>
        <button data-section="employee-overview">Employee Overview</button>
        <button data-section="task-assignment">Task Assignment</button>
        <button data-section="task-tracking">Task Tracking</button>
    </section>

    <section id="content-container">
        <!-- Dynamic content will load here -->
        <div id="user-profile" class="section-content">
            <form id="create-user-form">
                <h2>Basic Information</h2>
                <label for="first-name">First Name:</label>
                <input type="text" id="first-name" name="first-name" required>
                <label for="last-name">Last Name:</label>
                <input type="text" id="last-name" name="last-name" required>
                <label for="gender">Gender:</label>
                <select id="gender" name="gender" required>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                </select>
                <label for="division">Division:</label>
                <select id="division" name="division" required>
                    <option value="Regulatory Administration">Regulatory Administration</option>
                    <option value="Engineering">Engineering</option>
                    <option value="Human Resources">Human Resources</option>
                </select>
                <label for="status">Status:</label>
                <select id="status" name="status" required>
                    <option value="Division_head">Division head</option>
                    <option value="Unit_head">Unit head</option>
                    <option value="Unit_member">Unit member</option>
                </select>
                <label for="rank">Rank:</label>
                <select id="rank" name="rank" required>
                    <option value="Director">Director</option>
                    <option value="Deputy Director">Deputy Director</option>
                    <option value="Principal Manager">Principal Manager</option>
                    <option value="Chief Manager">Chief Manager</option>
                    <option value="Senior Manager">Senior Manager</option>
                    <option value="Manager">Manager</option>
                    <option value="Deputy Manager">Deputy Manager</option>
                    <option value="Assistant Manager">Assistant Manager</option>
                    <option value="Officer">Officer</option>
                </select>
                <label for="date-of-birth">Date of Birth:</label>
                <input type="date" id="date-of-birth" name="date-of-birth" required>
                <!-- Additional fields for certifications, degrees, etc. -->
                <hr>
                <h2>Professional Background</h2>
                <label for="qualifications">Qualifications/Education:</label>
                <textarea id="qualifications" name="qualifications" rows="4" required></textarea>
                <label for="experience">Experience:</label>
                <textarea id="experience" name="experience" rows="4" required></textarea>
                <button type="submit">Create Profile</button>
            </form>
        </div>

        <div id="organisational-objectives" class="section-content">
            <div id="objectives-container">
                <!-- Existing objectives will be listed here -->
            </div>
            <button id="add-objective-btn">Add Objective</button>

            <!-- Modal for creating new objective -->
            <div id="objective-modal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <form id="create-objective-form">
                        <input type="text" placeholder="Objective Title" name="title" required />
                        <textarea placeholder="Objective Description" name="description" required></textarea>
                        <button type="submit">Create Objective</button>
                    </form>
                </div>
            </div>
        </div>

        <div id="objectives-tree" class="section-content">
            <div id="tree-container">
                <!-- All objectives will be shown here in a tree -->
            </div>
        </div>

        <div id="employee-overview" class="section-content">
            <div id="employee-search">
                <input type="text" id="employee-search-input" placeholder="Search employees...">
                <!-- Suggestions will be displayed here -->
                <div id="employee-search-results"></div>
            </div>
            <div id="employee-info">
                <!-- Employee information will be loaded here -->
            </div>


            <!-- create 2 columns, 1 for the chart and one a smaller one to the right of the chart for the tooltip information -->

            <div id="employee-skills-chart">
                <div id="skillsRadarChart" class="radar-container">
                    <!-- Radar chart will be rendered here -->
                </div>
                <div id="radar-tooltip" class="radar-tooltip-container">
                    <div id="tooltip-content" class="radar-tooltip-content">
                        <p id="skill-axis"></p>
                        <p id="skill-category"></p>
                        <p id="skill-count"></p>
                        <p id="skill-percentage"></p>
                    </div>
                </div>
            </div>

        </div>

        <div id="task-assignment" class="section-content">
            <div id="task-assignment-tabs">
                <button data-tab="assign-tasks">Assign Tasks</button>
                <button data-tab="self-generated-tasks">Self-Generated Tasks</button>
            </div>

            <div id="assign-tasks" class="task-content active">
                <form id="assign-task-form">
                    <h2>Assign Task</h2>
                    <!-- <label for="task_id">Task ID:</label>
                    <input type="text" id="task_id" name="task_id" required> -->
                    <label for="task_title">Task Title:</label>
                    <input type="text" id="task_title" name="task_title" required>
                    <label for="task_description">Task Description:</label>
                    <textarea id="task_description" name="task_description" rows="4" required></textarea>
                    <label for="assignment_date">Assignment Date:</label>
                    <input type="date" id="assignment_date" name="assignment_date" value="<?php echo date('Y-m-d'); ?>" required>
                    <label for="expected_completion_date">Expected Completion Date:</label>
                    <input type="date" id="expected_completion_date" name="expected_completion_date" value="<?php echo date('Y-m-d'); ?>" required>
                    <label for="associated_objectives">Associated Objectives:</label>
                    <select id="associated_objectives" name="associated_objectives" required>
                        <!-- Options will be populated from the backend -->
                    </select>


                    <!-- KPA Section -->
                    <div class="kpa-section">
                        <h3>Key Performance Actions</h3>
                        <div id="kpa-container-assign">
                            <div class="kpa-input-group">
                                <input type="text" name="kpa_text[]" placeholder="Enter KPA" required>
                                <input type="date" name="kpa_date[]" required>
                                <button type="button" class="delete-kpa">Delete</button>
                            </div>
                        </div>
                        <button type="button" id="add-kpa-btn-assign" class="add-kpa-btn">Add KPA</button>
                    </div>

                    <button id="partial_submit" type="submit">Submit</button>
                </form>

                <div class="recommendations">
                    <h3>Recommendations</h3>
                    <ul>
                        <!-- Recommendations will be populated here -->
                        <li>
                            <input type="checkbox" id="staff_1">
                            <label for="staff_1">Staff ID 1 - 90% Match</label>
                        </li>
                        <li>
                            <input type="checkbox" id="staff_2">
                            <label for="staff_2">Staff ID 2 - 85% Match</label>
                        </li>
                        <!-- Add more recommendations as needed -->
                    </ul>
                </div>
            </div>

<div id="self-generated-tasks" class="task-content">
    <form id="self-generated-task-form">
        <h2>Create Self-Generated Task</h2>
        <label for="self_task_title">Task Title:</label>
        <input type="text" id="self_task_title" name="task_title" required>

        <label for="self_task_description">Task Description:</label>
        <textarea id="self_task_description" name="task_description" rows="4" required></textarea>

        <label for="self_start_date">Start Date:</label>
        <input type="date" id="self_start_date" name="start_date" required>

        <label for="self_expected_completion_date">Expected Completion Date:</label>
        <input type="date" id="self_expected_completion_date" name="expected_completion_date" required>

        <label for="self_associated_objectives">Associated Objectives:</label>
        <select id="self_associated_objectives" name="associated_objective" required>
            <!-- Options will be populated dynamically -->
        </select>

        <label for="self_participants">Number of Participants:</label>
        <input type="number" id="self_participants" name="participants" min="1" value="1" required>

        <!-- KPA Section -->
        <div class="kpa-section">
            <h3>Key Performance Actions</h3>
            <div id="kpa-container-self">
                <div class="kpa-input-group">
                    <input type="text" name="kpa_text[]" placeholder="Enter KPA" required>
                    <input type="date" name="kpa_date[]" required>
                    <button type="button" class="delete-kpa">Delete</button>
                </div>
            </div>
            <button type="button" id="add-kpa-btn-self" class="add-kpa-btn">Add KPA</button>
        </div>

        <button type="submit">Create Self-Generated Task</button>
    </form>

    <!-- Recommendations will be inserted here -->
    <div id="self-task-recommendations" class="recommendations" style="display: none;">
        <h3>Recommended Team Members</h3>
        <ul>
            <!-- Team member recommendations will be inserted here -->
        </ul>
    </div>
</div>

<!-- Add a new section for task approval -->
<div id="task-approval" class="section-content">
    <h2>Tasks Pending Approval</h2>
    <div id="tasks-for-approval-container">
        <!-- Tasks pending approval will be displayed here -->
    </div>
</div>

<!-- Add a modal for task approval -->
<div id="task-approval-modal" class="modal">
    <div class="modal-content">
        <span class="close" id="approval-close">&times;</span>
        <h2>Task Approval</h2>
        <div id="task-approval-details">
            <!-- Task details will be displayed here -->
        </div>
        <form id="task-approval-form">
            <input type="hidden" id="approval-task-id" name="task_id">
            <div class="approval-buttons">
                <button type="button" id="approve-task-btn" data-decision="approve">Approve</button>
                <button type="button" id="reject-task-btn" data-decision="reject">Reject</button>
            </div>
            <label for="approval-comments">Comments:</label>
            <textarea id="approval-comments" name="comments" rows="4"></textarea>
            <button type="submit">Submit Decision</button>
        </form>
    </div>
</div>

        </div>

        <div id="task-tracking" class="section-content">
            <h2>Task Progress</h2>
            <div id="task-progress-container" class="task-progress">
                <!-- Task progress items will be dynamically inserted here -->
            </div>
        </div>
    </section>

</main>

<!-- Calendar Heatmap Modal -->
<div id="heatmap-modal" class="heatmap-modal">
    <div class="heatmap-content">
        <button id="heatmap-close" class="heatmap-close">Close</button>
        <div id="calendar"></div>
    </div>
</div>

<!-- Draft Submission Modal -->
<div id="draft-modal" class="modal">
    <div class="modal-content">
        <span class="close" id="draft-close">&times;</span>
        <h2>Submit Draft</h2>
        <form id="draft-form">
            <div id="draft-history"></div>
            <textarea name="draft-content" placeholder="Enter your draft content" required></textarea>
            <input type="file" name="draft-file">
            <button type="submit">Submit Draft</button>
        </form>
    </div>
</div>

<!-- Review Modal -->
<div id="review-modal" class="modal">
    <div class="modal-content">
        <span class="close" id="review-close">&times;</span>
        <h2>Review Draft</h2>
        <form id="review-form">
            <div id="review-content"></div>
            <select name="review-status" required>
                <option value="Approved">Approve</option>
                <option value="Revisions Requested">Request Revisions</option>
            </select>
            <textarea name="review-comments" placeholder="Enter review comments"></textarea>
            <button type="submit">Submit Review</button>
        </form>
    </div>
</div>



<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js" charset="utf-8"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/4.10.2/d3.min.js" charset="utf-8"></script>
<script src="/static/assets/cal/dist/calendar-heatmap.min.js"></script>

<!-- Additional scripts -->
<script src="/static/assets/hr.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const taskTabs = document.querySelectorAll('#task-assignment-tabs button');
    const taskContents = document.querySelectorAll('.task-content');

    taskTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            taskContents.forEach(content => content.classList.remove('active'));
            document.getElementById(tabId).classList.add('active');
        });
    });

    // KPA functionality
    const addKpaBtnAssign = document.getElementById('add-kpa-btn-assign');
    const kpaContainerAssign = document.getElementById('kpa-container-assign');
    const addKpaBtnSelf = document.getElementById('add-kpa-btn-self');
    const kpaContainerSelf = document.getElementById('kpa-container-self');

    // Add event listener for assign tasks KPA button
    if (addKpaBtnAssign) {
        addKpaBtnAssign.addEventListener('click', () => {
            addKpaInputGroup(kpaContainerAssign);
        });
    }

    // Add event listener for self-generated tasks KPA button
    if (addKpaBtnSelf) {
        addKpaBtnSelf.addEventListener('click', () => {
            addKpaInputGroup(kpaContainerSelf);
        });
    }

    function addKpaInputGroup(container) {
        if (!container) {
            console.error('KPA container is null or undefined');
            return;
        }

        console.log('Adding KPA input group to container:', container.id);

        const kpaInputGroup = document.createElement('div');
        kpaInputGroup.className = 'kpa-input-group';
        kpaInputGroup.innerHTML = `
            <input type="text" name="kpa_text[]" placeholder="Enter KPA" required>
            <input type="date" name="kpa_date[]" required>
            <button type="button" class="delete-kpa">Delete</button>
        `;
        container.appendChild(kpaInputGroup);
        kpaInputGroup.querySelector('.delete-kpa').addEventListener('click', () => container.removeChild(kpaInputGroup));

        console.log('KPA input group added successfully');
    }

    // Form submissions
    const forms = {
        'assign-task-form': 'assigned',
        'self-generated-task-form': 'self-generated'
    };

    Object.entries(forms).forEach(([formId, requestType]) => {
        const form = document.getElementById(formId);
        form?.addEventListener('submit', e => {
            e.preventDefault();

            // Validate form before submission
            if (validateForm(form)) {
                submitTaskForm(formId, requestType);
            }
        });
    });

    // Form validation function
    function validateForm(form) {
        // Reset previous error states
        form.querySelectorAll('.error-field').forEach(field => {
            field.classList.remove('error-field');
        });

        const errorContainer = form.querySelector('.error');
        if (errorContainer) {
            errorContainer.style.display = 'none';
        }

        let isValid = true;

        // Check required fields
        form.querySelectorAll('[required]').forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('error-field');
                isValid = false;
            }
        });

        // Display error message if validation fails
        if (!isValid) {
            let errorMsg = document.createElement('div');
            errorMsg.className = 'error';
            errorMsg.textContent = 'PLEASE FILL IN ALL REQUIRED FIELDS';

            // Remove any existing error message
            const existingError = form.querySelector('.error');
            if (existingError) {
                existingError.remove();
            }

            form.appendChild(errorMsg);
        }

        return isValid;
    }

    function submitTaskForm(formId, requestType) {
        const formData = new FormData(document.getElementById(formId));
        const data = {
            request_type: requestType,
            kpas: []
        };

        const kpaTexts = formData.getAll('kpa_text[]');
        const kpaDates = formData.getAll('kpa_date[]');
        data.kpas = kpaTexts.map((text, i) => ({ text, date: kpaDates[i] }));

        Array.from(formData.entries()).forEach(([key, val]) => {
            if (!key.includes('kpa_')) data[key] = val;
        });

        fetch('/assign_task', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        })
        .then(handleResponse)
        .catch(handleError);
    }

    // Recommendations and assignments
    function displayRecommendations(ranking) {
        const container = document.querySelector('.recommendations ul');
        container.innerHTML = '';

        ranking.forEach(staff => {
            const li = document.createElement('li');
            li.innerHTML = `
                <input type="radio" id="staff_${staff.staff_id}" name="staff">
                <label for="staff_${staff.staff_id}">
                    ${staff.staff_id} - ${staff.match}% Match - ${staff.reasoning}
                </label>
            `;
            container.appendChild(li);
        });

        // Create a submit button for the recommendations
        const submitBtn = document.createElement('button');
        submitBtn.textContent = 'Assign Task';
        submitBtn.addEventListener('click', function() {
            const selectedStaff = [];
            const radios = document.querySelectorAll('.recommendations input[type="radio"]');
            radios.forEach(radio => {
                if (radio.checked) {
                    selectedStaff.push(radio.id.split('_')[1]);
                }
            });
            submitSelectedStaff(selectedStaff);
        });
        container.parentElement.appendChild(submitBtn);
    }

    function submitSelectedStaff(selectedStaff) {
        // Implement the logic to handle the selected staff
        console.log('Selected Staff:', selectedStaff);
        // You can make an AJAX request to the backend to assign the task to the selected staff
        fetch('/assign_task_to_staff', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ staff_ids: selectedStaff })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('Task assigned successfully');
            } else {
                alert('Error assigning task: ' + result.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    // Heatmap modal functionality
    const heatmapModal = document.getElementById('heatmap-modal');
    const heatmapClose = document.getElementById('heatmap-close');

    function showHeatmapModal() {
        heatmapModal.classList.add('active');
    }

    function closeHeatmapModal() {
        heatmapModal.classList.remove('active');
    }

    heatmapClose?.addEventListener('click', closeHeatmapModal);
    heatmapModal?.addEventListener('click', e => e.target === heatmapModal && closeHeatmapModal());

    // Draft submission modal elements
    const draftModal = document.getElementById('draft-modal');
    const draftClose = document.getElementById('draft-close');
    const draftForm = document.getElementById('draft-form');
    const reviewModal = document.getElementById('review-modal');
    const reviewForm = document.getElementById('review-form');

    // Current task and KPA tracking
    let currentTaskId = null;
    let currentProgressIndex = null;

    // Draft submission modal handling
    function showDraftModal(taskId, progressIndex) {
        currentTaskId = taskId;
        currentProgressIndex = progressIndex;
        draftModal.classList.add('active');
        loadExistingDrafts(taskId, progressIndex);
    }

    function closeDraftModal() {
        draftModal.classList.remove('active');
        currentTaskId = null;
        currentProgressIndex = null;
    }

    // Review modal handling
    function showReviewModal(taskId, progressIndex) {
        currentTaskId = taskId;
        currentProgressIndex = progressIndex;
        reviewModal.classList.add('active');
        loadDraftForReview(taskId, progressIndex);
    }

    function closeReviewModal() {
        reviewModal.classList.remove('active');
        currentTaskId = null;
        currentProgressIndex = null;
    }

    // Event listeners for modals
    draftClose?.addEventListener('click', closeDraftModal);
    draftModal?.addEventListener('click', e => e.target === draftModal && closeDraftModal());

    document.getElementById('review-close')?.addEventListener('click', closeReviewModal);
    reviewModal?.addEventListener('click', e => e.target === reviewModal && closeReviewModal());


    // Modified KPA item click handler
    function handleKpaClick(taskId, progressIndex, isRequester) {
    if (isRequester) {
        showReviewModal(taskId, progressIndex);
    } else {
        showDraftModal(taskId, progressIndex);
    }
}
// Fetch task progress
function fetchTaskProgress() {
    const employeeId = sessionStorage.getItem('staffId');
    fetch(`/get_employee_data/${employeeId}`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('task-progress-container');
            container.innerHTML = data.tasks.map(task => `
                <div id="progress">
                    <div id="progress-bar" style="width: ${calculateProgressWidth(task.progress)}%;"></div>
                    <ul id="progress-text">
                        ${task.progress.map((kpa, index) => `
                            <li class="step ${getProgressClass([kpa.status])} ${new Date(kpa.description.date) < new Date() ? 'past-date' : 'future-date'}"
                                data-task-id="${task.task_id}"
                                data-progress-index="${index}"
                                data-is-requester="${task.requester_id === data.staff_id}">
                                <label>${kpa.description.text}</label>
                                <small>${new Date(kpa.description.date).toLocaleDateString()}</small>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `).join('');

            // Add KPA click handlers
            container.querySelectorAll('.step').forEach(kpa => {
                kpa.addEventListener('click', (e) => {
                    if (!e.target.closest('.delete-draft')) {
                        const taskId = kpa.dataset.taskId;
                        const progressIndex = kpa.dataset.progressIndex;
                        const isRequester = kpa.dataset.isRequester === 'true';
                        handleKpaClick(taskId, progressIndex, isRequester);
                    }
                });
            });
        })
        .catch(handleError);
}


// This is a duplicate function definition that has been removed
// Review form handler
reviewForm?.addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(reviewForm);
    const reviewData = {
        status: formData.get('review-status'),
        comments: [formData.get('review-comments')],
        reviewer_id: sessionStorage.getItem('staffId')
    };

    try {
        const response = await fetch(`/tasks/${currentTaskId}/progress/${currentProgressIndex}/review-draft`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(reviewData)
        });

        const result = await response.json();
        if (result.error) throw new Error(result.error);

        alert('Review submitted successfully!');
        closeReviewModal();
        fetchTaskProgress();
    } catch (error) {
        alert(`Review failed: ${error.message}`);
    }
});


// Draft submission form handler
draftForm?.addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData(draftForm);
    const content = formData.get('draft-content');

    try {
        const response = await fetch(`/tasks/${currentTaskId}/progress/${currentProgressIndex}/submit-draft`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ content })
        });

        const result = await response.json();
        if (result.error) throw new Error(result.error);

        alert('Draft submitted successfully!');
        closeDraftModal();
        fetchTaskProgress();
    } catch (error) {
        alert(`Submission failed: ${error.message}`);
    }
});


    // Helper functions
    async function loadExistingDrafts(taskId, progressIndex) {
    try {
        const response = await fetch(`/get_task?task_id=${taskId}`);
        const task = await response.json();
        const drafts = task.progress[progressIndex].drafts;

        const draftList = document.getElementById('draft-history');
        draftList.innerHTML = drafts.map(draft => `
            <div class="draft-item">
                <p>${new Date(draft.submitted_at).toLocaleString()}</p>
                <p>Status: ${draft.status}</p>
                ${draft.comments?.length ? `
                <div class="comments">
                    ${draft.comments.map(c => `<p>${c}</p>`).join('')}
                </div>` : ''}
                <button class="delete-draft" data-draft-id="${draft.draft_id}">Delete</button>
            </div>
        `).join('');
    } catch (error) {
        console.error('Error loading drafts:', error);
    }
}



async function loadDraftForReview(taskId, progressIndex) {
    try {
        const response = await fetch(`/get_task?task_id=${taskId}`);
        const task = await response.json();
        const kpa = task.progress[progressIndex];

        document.getElementById('review-content').innerHTML = `
            <h3>${kpa.description.text}</h3>
            ${kpa.drafts.map(draft => `
                <div class="draft-review">
                    <p>Submitted: ${new Date(draft.submitted_at).toLocaleString()}</p>
                    <pre>${draft.content}</pre>
                </div>
            `).join('')}
        `;
    } catch (error) {
        console.error('Error loading draft for review:', error);
    }
}
    // Function to fetch current user from Flask session
    function fetchCurrentUser() {
        fetch('/get_current_user')
            .then(response => response.json())
            .then(data => {
                if (data.staff_id) {
                    // Store the staff_id in sessionStorage for client-side access
                    sessionStorage.setItem('staffId', data.staff_id);
                    console.log('Staff ID stored in sessionStorage:', data.staff_id);
                } else {
                    console.error('No staff_id in response:', data);
                }
                // Now that we have the user ID, we can fetch the task progress
                fetchTaskProgress();
            })
            .catch(error => {
                console.error('Error fetching current user:', error);
                // Still try to fetch task progress with fallback
                fetchTaskProgress();
            });
    }

    // Initialization
    fetchCurrentUser();
    fetchAssociatedObjectives();
    fetchNotifications();
});

// Helper functions
function calculateProgressWidth(progress) {
    const approved = progress.filter(p => p.status === 'Approved').length;
    return Math.round((approved / progress.length) * 100) || 0;
}

// Helper function to get progress class
function getProgressClass(statuses) {
    if (statuses.every(s => s === 'Approved')) return 'completed';
    if (statuses.includes('Revisions Requested')) return 'revisions-needed';
    if (statuses.includes('Under Review')) return 'under-review';
    if (statuses.some(s => s !== 'Not Started')) return 'in-progress';
    return 'not-started';
}

// Heatmap functions
function initFlaskCalendarHeatmap(flaskData) {
    // Clear previous heatmap
    const calendarContainer = document.getElementById('calendar');
    if (calendarContainer) {
        calendarContainer.innerHTML = ''; // Clear existing content
    }

    const heatmapData = generateHeatmapData(flaskData);
    if (!heatmapData.length) {
        console.warn("No heatmap data");
        return;
    }

    // Initialize new heatmap
    calendarHeatmap.init(
        heatmapData,
        'calendar',
        '#cd2327',
        'year',
        val => console.log("Heatmap event:", val)
    );
}

function generateHeatmapData(flaskData) {
    const groupedData = (flaskData.heatmap_data || []).reduce((acc, entry) => {
        const dateKey = entry.date.split('T')[0];
        if (!acc[dateKey]) {
            acc[dateKey] = {
                date: new Date(dateKey),
                details: [],
                summary: []
            };
        }
        acc[dateKey].details.push({
            name: entry.task_title,
            value: entry.value,
            description: entry.description
        });
        acc[dateKey].summary = acc[dateKey].details; // Copy details to summary
        return acc;
    }, {});

    return Object.values(groupedData).map(entry => ({
        date: entry.date,
        details: entry.details,
        summary: entry.summary,
        init() {
            this.total = this.details.reduce((acc, d) => acc + d.value, 0);
            return this;
        }
    }).init());
}

// Generic handlers
function handleResponse(response) {
    if (!response.ok) throw new Error('Network error');
    return response.json().then(data => {
        if (data.error) throw new Error(data.error);
        if (data.ranking) {
            // Determine which type of task this is based on the active tab
            const selfGeneratedTab = document.getElementById('self-generated-tasks');
            if (selfGeneratedTab && selfGeneratedTab.classList.contains('active')) {
                // For self-generated tasks
                displayRecommendedTeam(data.ranking);
            } else {
                // For assigned tasks
                displayRecommendations(data.ranking);
            }
        }
        if (data.message) alert(data.message);
        return data;
    });
}

function handleError(error) {
    console.error('Error:', error);

    // Create or update error message element
    let errorContainer = document.getElementById('global-error-message');
    if (!errorContainer) {
        errorContainer = document.createElement('div');
        errorContainer.id = 'global-error-message';
        errorContainer.className = 'error';
        document.querySelector('main').prepend(errorContainer);
    }

    errorContainer.textContent = `ERROR: ${error.message}`;
    errorContainer.style.display = 'block';

    // Hide error after 5 seconds
    setTimeout(() => {
        errorContainer.style.display = 'none';
    }, 5000);
}

// Fetch associated objectives
function fetchAssociatedObjectives() {
    fetch('/get_associated_objectives')
        .then(response => response.json())
        .then(data => {
            const associatedObjectivesSelect = document.getElementById('associated_objectives');
            const selfAssociatedObjectivesSelect = document.getElementById('self_associated_objectives');

            if (associatedObjectivesSelect && selfAssociatedObjectivesSelect) {
                data.objectives.forEach(objective => {
                    const option = document.createElement('option');
                    option.value = objective.id;
                    option.textContent = objective.title;
                    associatedObjectivesSelect.appendChild(option);

                    const selfOption = option.cloneNode(true);
                    selfAssociatedObjectivesSelect.appendChild(selfOption);
                });
            } else {
                console.error('Element not found: associated_objectives or self_associated_objectives');
            }
        })
        .catch(handleError);
}

// Fetch notifications
function fetchNotifications() {
    fetch('/get_notifications')
        .then(response => response.json())
        .then(data => {
            if (data.notifications) {
                displayNotifications(data.notifications);
            }
        })
        .catch(handleError);
}

// Display notifications
function displayNotifications(notifications) {
    const notificationsContent = document.getElementById("notifications-content");

    // Clear existing notifications
    notificationsContent.innerHTML = "";

    if (notifications.length === 0) {
        notificationsContent.innerHTML = "<p>No new notifications.</p>";
        return;
    }

    // Create and append notification elements
    notifications.forEach(notification => {
        const notificationElement = document.createElement("div");
        notificationElement.className = "notification";
        notificationElement.innerHTML = `
            <p><strong>${notification.type.toUpperCase()}</strong></p>
            <p>${notification.message}</p>
            <small>${new Date(notification.created_at).toLocaleString()}</small>
        `;

        // Mark as read when clicked
        notificationElement.addEventListener("click", () => {
            markNotificationAsRead(notification._id);
            notificationElement.style.opacity = "0.6"; // Visual feedback for read notifications
        });

        notificationsContent.appendChild(notificationElement);
    });
}

// Mark notification as read
async function markNotificationAsRead(notificationId) {
    try {
        const response = await fetch(`/api/notifications/mark-as-read/${notificationId}`, {
            method: "PUT"
        });
        if (!response.ok) {
            throw new Error("Failed to mark notification as read");
        }
    } catch (error) {
        console.error("Error marking notification as read:", error);
    }
}

// Global function to display recommended team members for self-generated tasks
function displayRecommendedTeam(ranking) {
    // Get the form and recommendations container
    const selfGeneratedTaskForm = document.getElementById('self-generated-task-form');
    let recommendationsContainer = document.getElementById('self-task-recommendations');

    // If container doesn't exist, create it
    if (!recommendationsContainer) {
        recommendationsContainer = document.createElement('div');
        recommendationsContainer.id = 'self-task-recommendations';
        recommendationsContainer.className = 'recommendations';
        recommendationsContainer.innerHTML = '<h3>Recommended Team Members</h3>';

        // Add container after the form
        if (selfGeneratedTaskForm) {
            selfGeneratedTaskForm.parentNode.appendChild(recommendationsContainer);
        } else {
            // Fallback if form not found
            document.querySelector('.task-content').appendChild(recommendationsContainer);
        }
    } else {
        // Clear existing content
        recommendationsContainer.innerHTML = '<h3>Recommended Team Members</h3>';
    }

    // Make the container visible
    recommendationsContainer.style.display = 'block';

    // Create list for recommendations
    const recommendationsList = document.createElement('ul');

    // Add each recommended team member
    ranking.forEach(staff => {
        const li = document.createElement('li');
        li.innerHTML = `
            <input type="checkbox" id="team_${staff.staff_id}" name="team_members" value="${staff.staff_id}">
            <label for="team_${staff.staff_id}">
                ${staff.staff_id} - ${staff.match}% Match - ${staff.reasoning}
            </label>
        `;
        recommendationsList.appendChild(li);
    });

    recommendationsContainer.appendChild(recommendationsList);

    // Add button to invite selected team members
    const inviteButton = document.createElement('button');
    inviteButton.textContent = 'Invite Selected Team Members';
    inviteButton.addEventListener('click', function() {
        inviteTeamMembers();
    });

    recommendationsContainer.appendChild(inviteButton);
}

// Global function to invite team members
function inviteTeamMembers() {
    const selectedMembers = Array.from(document.querySelectorAll('input[name="team_members"]:checked')).map(checkbox => checkbox.value);

    if (selectedMembers.length === 0) {
        alert('Please select at least one team member to invite.');
        return;
    }

    // Get task ID from session (assuming it's stored during task creation)
    fetch('/invite_team_members', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            selected_members: selectedMembers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error: ' + data.error);
        } else {
            alert('Team members invited successfully!');
            // Hide recommendations container
            document.getElementById('self-task-recommendations').style.display = 'none';
        }
    })
    .catch(error => {
        console.error('Error inviting team members:', error);
        alert('Error inviting team members. Please try again.');
    });
}


//##################################################claude additions##################################################//


// Self-Generated Tasks UI Enhancement
// This code will enhance the HR_home.html task assignment tab with self-generated tasks functionality

document.addEventListener('DOMContentLoaded', function() {
    // Existing tab functionality is already defined in HR_home.html

    // Add event listener for the "Self-Generated Tasks" form
    const selfGeneratedTaskForm = document.getElementById('self-generated-task-form');
    if (selfGeneratedTaskForm) {
        selfGeneratedTaskForm.addEventListener('submit', function(event) {
            event.preventDefault();
            submitSelfGeneratedTask();
        });
    }

    // Load objectives for the associated objectives dropdown in self-generated tasks form
    loadAssociatedObjectives();

    // Function to load objectives for the associated objectives dropdown
    function loadAssociatedObjectives() {
        fetch('/get_associated_objectives')
            .then(response => response.json())
            .then(data => {
                const associatedObjectivesSelect = document.getElementById('self_associated_objectives');

                if (associatedObjectivesSelect) {
                    // Clear existing options
                    associatedObjectivesSelect.innerHTML = '';

                    // Add a default option
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Select an objective';
                    defaultOption.disabled = true;
                    defaultOption.selected = true;
                    associatedObjectivesSelect.appendChild(defaultOption);

                    // Add options for each objective
                    data.objectives.forEach(objective => {
                        const option = document.createElement('option');
                        option.value = objective.id;
                        option.textContent = objective.title;
                        associatedObjectivesSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading objectives:', error);
            });
    }

    // Function to submit self-generated task
    // function submitSelfGeneratedTask() {
    //     const taskTitle = document.getElementById('self_task_title').value;
    //     const taskDescription = document.getElementById('self_task_description').value;
    //     const startDate = document.getElementById('self_start_date').value;
    //     const expectedCompletionDate = document.getElementById('self_expected_completion_date').value;
    //     const associatedObjective = document.getElementById('self_associated_objectives').value;
    //     const participants = document.getElementById('self_participants').value;

    //     // Get KPAs
    //     const kpasText = Array.from(document.querySelectorAll('#kpa-container-self input[name="kpa_text[]"]')).map(input => input.value);
    //     const kpasDates = Array.from(document.querySelectorAll('#kpa-container-self input[name="kpa_date[]"]')).map(input => input.value);

    //     // Create array of KPA objects
    //     const kpas = kpasText.map((text, index) => ({
    //         text: text,
    //         date: kpasDates[index] || ''
    //     }));

    //     // Prepare task data
    //     const taskData = {
    //         task_title: taskTitle,
    //         task_description: taskDescription,
    //         start_date: startDate,
    //         expected_completion_date: expectedCompletionDate,
    //         associated_objective: associatedObjective,
    //         participants: participants,
    //         kpas: kpas,
    //         request_type: 'self-generated'
    //     };

    //     // Submit task data to server
    //     fetch('/assign_task', {
    //         method: 'POST',
    //         headers: {
    //             'Content-Type': 'application/json'
    //         },
    //         body: JSON.stringify(taskData)
    //     })
    //     .then(response => response.json())
    //     .then(data => {
    //         if (data.error) {
    //             alert('Error: ' + data.error);
    //         } else {
    //             // If task created successfully
    //             if (data.ranking) {
    //                 // Display recommended team members
    //                 displayRecommendedTeam(data.ranking);
    //             } else {
    //                 alert('Task created successfully!');
    //                 // Reset form
    //                 selfGeneratedTaskForm.reset();
    //             }
    //         }
    //     })
    //     .catch(error => {
    //         console.error('Error creating task:', error);
    //         alert('Error creating task. Please try again.');
    //     });
    // }

    // Note: displayRecommendedTeam and inviteTeamMembers functions are now defined globally

    // Note: KPA button functionality is already handled in the main script
    // We don't need to add another event listener here





// / Add Task Approval button to the button row
    const buttonRow = document.getElementById('button-row');
    if (buttonRow) {
        const approvalButton = document.createElement('button');
        approvalButton.setAttribute('data-section', 'task-approval');
        approvalButton.textContent = 'Task Approval';
        buttonRow.appendChild(approvalButton);

        // Add event listener for the new button
        approvalButton.addEventListener('click', function() {
            loadSectionContent('task-approval');
            loadTasksForApproval();
        });
    }

    // Function to load tasks pending approval
    function loadTasksForApproval() {
        fetch('/get_tasks_for_approval')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('tasks-for-approval-container');
                if (container) {
                    // Clear existing content
                    container.innerHTML = '';

                    if (data.tasks && data.tasks.length > 0) {
                        // Create table for tasks
                        const table = document.createElement('table');
                        table.className = 'tasks-table';

                        // Create table header
                        const thead = document.createElement('thead');
                        thead.innerHTML = `
                            <tr>
                                <th>Task ID</th>
                                <th>Title</th>
                                <th>Requester</th>
                                <th>Created At</th>
                                <th>Associated Objective</th>
                                <th>Actions</th>
                            </tr>
                        `;
                        table.appendChild(thead);

                        // Create table body
                        const tbody = document.createElement('tbody');

                        // Add each task as a row
                        data.tasks.forEach(task => {
                            const tr = document.createElement('tr');
                            tr.innerHTML = `
                                <td>${task.task_id}</td>
                                <td>${task.task_title}</td>
                                <td>${task.requester_name || task.requester_id}</td>
                                <td>${new Date(task.created_at).toLocaleDateString()}</td>
                                <td>${task.objective_title || 'N/A'}</td>
                                <td>
                                    <button class="view-task-btn" data-task-id="${task.task_id}">View</button>
                                </td>
                            `;
                            tbody.appendChild(tr);
                        });

                        table.appendChild(tbody);
                        container.appendChild(table);

                        // Add event listeners for view buttons
                        const viewButtons = document.querySelectorAll('.view-task-btn');
                        viewButtons.forEach(button => {
                            button.addEventListener('click', function() {
                                const taskId = this.getAttribute('data-task-id');
                                showTaskApprovalModal(taskId);
                            });
                        });
                    } else {
                        container.innerHTML = '<p>No tasks pending approval.</p>';
                    }
                }
            })
            .catch(error => {
                console.error('Error loading tasks for approval:', error);
            });
    }

    // Function to show task approval modal
    function showTaskApprovalModal(taskId) {
        // Fetch task details
        fetch(`/get_task?task_id=${taskId}`)
            .then(response => response.json())
            .then(task => {
                const detailsContainer = document.getElementById('task-approval-details');
                if (detailsContainer) {
                    // Populate task details
                    detailsContainer.innerHTML = `
                        <h3>${task.task_title}</h3>
                        <p><strong>Description:</strong> ${task.task_description}</p>
                        <p><strong>Requester:</strong> ${task.requester_id}</p>
                        <p><strong>Created:</strong> ${new Date(task.created_at).toLocaleString()}</p>
                        <p><strong>Expected Completion:</strong> ${new Date(task.expected_completion_date).toLocaleDateString()}</p>

                        <h4>Key Performance Actions:</h4>
                        <ul>
                            ${task.progress.map(kpa => `
                                <li>
                                    ${kpa.description.text}
                                    (Due: ${new Date(kpa.description.date).toLocaleDateString()})
                                </li>
                            `).join('')}
                        </ul>

                        <h4>Team Members:</h4>
                        <ul>
                            ${task.team_members.map(member => `<li>${member}</li>`).join('')}
                        </ul>
                    `;

                    // Set task ID in the form
                    document.getElementById('approval-task-id').value = taskId;

                    // Show the modal
                    document.getElementById('task-approval-modal').style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error fetching task details:', error);
            });
    }

    // Handle approval form submission
    const approvalForm = document.getElementById('task-approval-form');
    if (approvalForm) {
        approvalForm.addEventListener('submit', function(event) {
            event.preventDefault();

            const taskId = document.getElementById('approval-task-id').value;
            const comments = document.getElementById('approval-comments').value;
            const decision = document.querySelector('button[data-decision].active').getAttribute('data-decision');

            submitTaskApproval(taskId, decision, comments);
        });
    }

    // Handle approve/reject button clicks
    const approveBtn = document.getElementById('approve-task-btn');
    const rejectBtn = document.getElementById('reject-task-btn');

    if (approveBtn && rejectBtn) {
        approveBtn.addEventListener('click', function() {
            setActiveDecision(this);
        });

        rejectBtn.addEventListener('click', function() {
            setActiveDecision(this);
        });
    }

    function setActiveDecision(button) {
        // Remove active class from all decision buttons
        document.querySelectorAll('button[data-decision]').forEach(btn => {
            btn.classList.remove('active');
        });

        // Add active class to clicked button
        button.classList.add('active');
    }

    function submitTaskApproval(taskId, decision, comments) {
        fetch('/approve_task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                task_id: taskId,
                decision: decision,
                comments: comments
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error: ' + data.error);
            } else {
                alert(data.message);

                // Close the modal
                document.getElementById('task-approval-modal').style.display = 'none';

                // Reload tasks for approval
                loadTasksForApproval();
            }
        })
        .catch(error => {
            console.error('Error submitting task approval:', error);
        });
    }

    // Close modal when clicking the close button or outside the modal
    const approvalClose = document.getElementById('approval-close');
    if (approvalClose) {
        approvalClose.addEventListener('click', function() {
            document.getElementById('task-approval-modal').style.display = 'none';
        });
    }

    window.addEventListener('click', function(event) {
        const modal = document.getElementById('task-approval-modal');
        if (modal && event.target === modal) {
            modal.style.display = 'none';
        }
    });
});












</script>

</body>
</html>