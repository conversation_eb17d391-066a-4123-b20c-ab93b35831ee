import json
import os
import ast
from datetime import datetime, timedelta
import uuid
from flask import Flask, flash, jsonify, render_template, redirect, request, session, url_for
from flask_pymongo import PyMongo, pymongo
from pymongo import UpdateMany
from bson.objectid import ObjectId
from bson.json_util import dumps
import pandas as pd
import openai
from collections import Counter
from itertools import chain



app = Flask(__name__)
openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

app.config["MONGO_URI"] ="mongodb+srv://GaudKing_Chapper:<EMAIL>/control?retryWrites=true&w=majority"
app.config["SECRET_KEY"] = "your_secret_key"  # Add a secret key for session management

mongo = PyMongo(app)

# Dummy user data for demonstration


@app.route("/lobby", methods=["GET"], endpoint="lobby")
def home():
    session['page'] = 'home'
    return render_template('fake.html')



@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        staff_id = request.form['staff_id']
        password = request.form['password']
        confirm_password = request.form['confirm_password']

        # Validate input fields
        if not staff_id:
            return render_template('register.html', error="STAFF ID REQUIRED", error_type="staff_id")
        if not password:
            return render_template('register.html', error="PASSWORD REQUIRED", error_type="password")
        if not confirm_password:
            return render_template('register.html', error="CONFIRM PASSWORD REQUIRED", error_type="confirm_password")

        # Check if passwords match
        if password != confirm_password:
            return render_template('register.html', error="PASSWORDS DO NOT MATCH", error_type="confirm_password")

        # Password strength validation
        if len(password) < 6:
            return render_template('register.html', error="PASSWORD TOO SHORT (MIN 6 CHARS)", error_type="password")

        try:
            # Check if user already exists
            user_cursor = mongo.db.profiles.find_one({'staff_id': staff_id})

            if user_cursor and 'staff_id' in user_cursor and staff_id == user_cursor['staff_id']:
                return render_template('register.html', error="STAFF ID ALREADY EXISTS", error_type="staff_id")

            # Create new user
            if user_cursor:
                mongo.db.profiles.update_one({'staff_id': staff_id}, {'$set': {'password': password}})
            else:
                # If user doesn't exist, create a new profile
                mongo.db.profiles.insert_one({
                    'staff_id': staff_id,
                    'password': password,
                    'created_at': datetime.now()
                })

            session['staff_id'] = staff_id
            return redirect(url_for('HR_home'))

        except Exception as e:
            print(f"Registration error: {str(e)}")
            return render_template('register.html', error="SERVER ERROR", error_type="general")

    return render_template('register.html')



@app.route('/', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        staff_id = request.form['staff_id']
        password = request.form['password']

        # Validate input fields
        if not staff_id:
            return render_template('login.html', error="STAFF ID REQUIRED", error_type="staff_id")
        if not password:
            return render_template('login.html', error="PASSWORD REQUIRED", error_type="password")

        try:
            # Find user in database
            user_cursor = mongo.db.profiles.find({'staff_id': staff_id})
            user_list = list(user_cursor)

            # Check if user exists
            if not user_list:
                return render_template('login.html', error="STAFF ID NOT FOUND", error_type="staff_id")

            # Check if user has password field
            if 'password' not in user_list[0]:
                return render_template('login.html', error="ACCOUNT ERROR", error_type="general")

            # Check credentials
            if staff_id == user_list[0]['staff_id'] and user_list[0]['password'] == password:
                session['staff_id'] = staff_id
                return redirect(url_for('lobby'))
            else:
                return render_template('login.html', error="INVALID CREDENTIALS", error_type="general")
        except Exception as e:
            print(f"Login error: {str(e)}")
            return render_template('login.html', error="SERVER ERROR", error_type="general")

    return render_template('login.html')





@app.route("/HR_home", methods=["GET"])
def hr_home():
    session['page'] = 'HR_home'
    return render_template('HR_home.html')

@app.route("/get_current_user", methods=["GET"])
def get_current_user():
    """Return the current user's ID from the session"""
    if 'staff_id' in session:
        return jsonify({"staff_id": session['staff_id']})
    else:
        return jsonify({"error": "No user logged in"}), 401

@app.route('/create_staff', methods=['POST'])
def create_staff():
    staff_data = request.get_json()

    # Insert staff data into MongoDB collection
    staff_id = mongo.db.staff.insert_one(staff_data).inserted_id

    session['last_created_staff_id'] = str(staff_id)

    return 'Staff created with ID: ' + str(staff_id)

@app.route('/search_employees', methods=['GET'])
def employee_search():
    search_term = request.args.get('search', '').strip()

    # Convert cursor to list before processing
    results_list = list(mongo.db.profiles.find({"$text": {"$search": search_term}}))
    # print(pd.DataFrame(results_list))

    # Now you can safely iterate over results_list as many times as you want
    response = [{"id": str(emp["staff_id"]), "name": emp["first_name"] + " " + emp["last_name"]} for emp in results_list]

    session['search_results'] = response

    return jsonify(response)

# Helper function to convert ObjectId to string
def serialize_doc(doc):
    doc['_id'] = str(doc['_id'])  # Convert ObjectId to string
    return doc

# Route to get associated objectives based on user's division
@app.route('/get_associated_objectives')
def get_associated_objectives():
    if 'staff_id' not in session:
        return jsonify({"error": "Unauthorized"}), 401

    staff_id = session['staff_id']
    user = mongo.db.profiles.find_one({"staff_id": staff_id})

    if not user:
        return jsonify({"error": "User not found"}), 404

    # Get user division
    division = user.get('division')

    # Find objectives associated with user's division
    # For staff, get all organization, division, and unit objectives
    pipeline = [
        {"$match": {"$or": [
            {"type": "organisational"},
            {"division": division, "type": "divisional"},
            {"division": division, "type": "unit"}
        ]}},
        {"$project": {
            "_id": 0,
            "id": {"$toString": "$_id"},
            "title": 1,
            "description": 1,
            "type": 1
        }}
    ]

    objectives = list(mongo.db.objectives.aggregate(pipeline))

    return jsonify({"objectives": objectives}), 200

@app.route("/objectives", methods=["GET"])
def show_objectives():
    objectives_pull = mongo.db.objectives.find()
    objectives = [{
        "title": i["title"],
        "description": i["description"],
        'parent_title':i['parent_title'] if 'parent_title' in i else "",
        'type':i['type'],
        "id":str(i["_id"])
    } for i in objectives_pull ]

    session['objectives'] = objectives

    return jsonify({"objectives":objectives})

@app.route("/objectives/add", methods=["POST"])
def add_objective():
    title = request.form.get("title")
    description = request.form.get("description")
    objective_type = request.form.get('type')
    objective = {
        "title": title,
        "description": description,
        "type": objective_type
    }
    # if parent_objective_id:
    #     objective["parent_objective_id"] = parent_objective_id
    result = mongo.db.objectives.insert_one(objective)

    # Convert ObjectId to string for JSON serialization
    objective_id = str(result.inserted_id)

    session['last_added_objective_id'] = objective_id

    return jsonify({'status':'added', 'id': objective_id})

@app.route('/delete_objective/<row_id>', methods=['POST'])
def delete_objective(row_id):
    print(row_id)
    # Perform deletion operation
    result = mongo.db.objectives.delete_one({'_id': ObjectId(row_id)})

    if result.deleted_count > 0:
        session['last_deleted_objective_id'] = row_id
        return jsonify({'status': 'deleted'}), 200
    else:
        return jsonify({'status': 'error'}), 404

@app.route('/get_page_content', methods=['GET'])
def page_select():
    page_dict = {'1':'/HR_home', '2':'/HR_home', '3':'/HR_home', '4':'/HR_home'}
    # Accessing the 'paneIndex' query parameter
    pane_destination = request.args.get('destination')

    print(pane_destination)

    if pane_destination:
        # Logic based on pane_index
        session['current_page'] = page_dict[pane_destination]
        return jsonify({'tab': page_dict[pane_destination]})
    else:
        return jsonify({'tab': 'error'})

# Utility function
def clean_and_parse_skills(skills_text):
    """Clean and parse the skills text from GPT response"""
    try:
        # Remove ```python and ``` markers if present
        cleaned_text = skills_text.replace("```python", "").replace("```", "").strip()

        # Safely evaluate the string to a Python list
        skills_list = ast.literal_eval(cleaned_text)

        return skills_list
    except Exception as e:
        print(f"Error parsing skills: {str(e)}")
        return []

def extract_skills_from_tasks_gpt4O(staff_id):

    """Extract skills from staff tasks using GPT-4"""
    try:
        # Fetch all tasks for the staff member
        tasks = list(mongo.db.task_completion_history.find({"task_assignee": staff_id}))

        if not tasks:
            print("No tasks found for staff_id:", staff_id)
            return []

        # Prepare task descriptions for analysis
        task_descriptions = []
        for task in tasks:
            progress_updates = " ".join(
                [p.get('description', '') for p in task.get('progress', [])]
            )
            task_description = (
                f"Task Title: {task.get('task_title', '')}\n"
                f"Description: {task.get('task_description', '')}\n"
                f"Progress Updates: {progress_updates}"
            )
            task_descriptions.append(task_description)

        response = openai_client.chat.completions.create(
            model="o1-mini",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f'''Analyze the following tasks, their descriptions and the progress updates and extract relevant skills.
                            \n Return the result as a Python list of tuples, where each tuple contains (skill_name, category). The categories are 'soft' for soft skills and 'hard' for hard skills.
                            \n Example: [('Presentation Delivery', 'soft'), ('Python Programming', 'hard')]
                            \n\n{task_descriptions}'''
                        }
                    ]
                }
            ]
        )

        # Parse GPT response to extract skills
        skills_text = response.choices[0].message.content
        print("Raw GPT response:", skills_text)  # Log the raw response

        skills_list = clean_and_parse_skills(skills_text)
        print('Parsed skills:', skills_list)  # Log the parsed skills

        return skills_list

    except Exception as e:
        print(f"Error in extract_skills_from_tasks_gpt4O: {str(e)}")
        return []

# Convert 'recommendations' from string to dict if necessary
def process_fields(doc):
    if 'recommendations' in doc and isinstance(doc['recommendations'], str):
        try:
            # Safely evaluate the string to a dictionary
            doc['recommendations'] = ast.literal_eval(doc['recommendations'])
        except (ValueError, SyntaxError):
            # Handle parsing errors
            doc['recommendations'] = {}
    return doc


def should_update_skills(staff_id):
    """
    Check if skills need to be updated based on:
    1. Last skills update time
    2. Recent task completions

    Args:
        staff_id (str): The ID of the staff member.

    Returns:
        bool: True if an update is required, False otherwise.
    """
    try:
        # Get the skills document
        skills_doc = mongo.db.skills.find_one({"staff_id": staff_id})

        # If no skills document exists, definitely need an update
        if not skills_doc:
            print("No skills document exists. Update required.")
            return True

        # Get the last updated time from the skills document
        last_updated = skills_doc.get('last_updated')
        if not last_updated:
            print("No last updated time found. Update required.")
            return True

        # Ensure last_updated is a datetime object
        if isinstance(last_updated, str):
            last_updated = datetime.strptime(last_updated, "%Y-%m-%d")

        # Get the most recent task completion date
        latest_task = mongo.db.task_completion_history.find_one(
            {"task_assignee": staff_id},
            sort=[("completion_date", pymongo.DESCENDING)]
        )

        if not latest_task:
            print("No tasks found. No update required.")
            return False  # No tasks to analyze

        latest_completion = latest_task.get('completion_date')
        if not latest_completion:
            print("No completion date found in the latest task. No update required.")
            return False

        # Ensure latest_completion is a datetime object
        if isinstance(latest_completion, str):
            latest_completion = datetime.strptime(latest_completion, "%Y-%m-%d")

        # Check if there are any tasks completed after the last skills update
        if latest_completion > last_updated:
            print("Tasks completed after the last skills update. Update required.")
            return True

        # Check if it's been more than a day since the last update
        if datetime.now() - last_updated > timedelta(days=1):
            print("More than a day since the last update. Update required.")
            return True

        print("No update required.")
        return False

    except Exception as e:
        print(f"Error checking skills update status: {str(e)}")
        return True  # Default to updating if there's an error



def get_unique_skills(existing_skills, new_skills):
    """
    Merge existing skills with new skills, counting occurrences.
    Each skill is a tuple: (skill_name, skill_category)
    Returns a list of dictionaries: [{'skill': ..., 'category': ..., 'count': ...}, ...]
    """
    # Convert existing_skills from list of dicts to list of tuples
    if existing_skills and isinstance(existing_skills[0], dict):
        existing_skills = [(s['skill'], s['category']) for s in existing_skills]

    # Ensure new_skills is a list of tuples
    if new_skills and isinstance(new_skills[0], dict):
        new_skills = [(s['skill'], s['category']) for s in new_skills]

    # Combine existing and new skills
    combined_skills = existing_skills + new_skills

    # Count occurrences of each (skill, category) pair
    skill_counter = Counter(combined_skills)

    # Convert to list of dictionaries
    unique_skills = [
        {"skill": skill, "category": category, "count": count}
        for (skill, category), count in skill_counter.items()
    ]

    return unique_skills

def update_staff_skills(staff_id):
    """
    Update staff skills if necessary
    """
    try:
        if should_update_skills(staff_id):
            print("Updating skills for staff_id:", staff_id)
            # Get existing skills
            skills_doc = mongo.db.skills.find_one({"staff_id": staff_id})
            existing_skills = skills_doc.get('skills', []) if skills_doc else []
            print("Existing skills (before conversion):", existing_skills)

            # Get new skills
            new_skills = extract_skills_from_tasks_gpt4O(staff_id)
            print("New skills (before conversion):", new_skills)

            # Combine existing and new skills
            combined_skills = get_unique_skills(existing_skills, new_skills)
            print("Combined skills:", combined_skills)

            # Update database
            skills_update = {
                "staff_id": staff_id,
                "last_updated": datetime.now(),
                "skills": combined_skills
            }

            mongo.db.skills.update_one(
                {"staff_id": staff_id},
                {"$set": skills_update},
                upsert=True
            )
            return combined_skills
        else:
            # Return existing skills if no update needed
            print("No need to update skills for staff_id:", staff_id)
            skills_doc = mongo.db.skills.find_one({"staff_id": staff_id})
            print('skills doc:', skills_doc)
            return skills_doc.get('skills', []) if skills_doc else []

    except Exception as e:
        print(f"Error updating staff skills: {str(e)}")
        return []

# @app.route('/get_employee_data/<employee_id>', methods=['GET'])
# def get_employee_data(employee_id):
#     try:
#         # Fetch the employee profile
#         employee_profile = mongo.db.profiles.find_one({"staff_id": employee_id})
#         if not employee_profile:
#             return jsonify({"error": "Employee not found"}), 404

#         # Convert MongoDB document to a serializable dict, excluding '_id'
#         employee_data = {k: v for k, v in employee_profile.items() if k != '_id'}
#         employee_data['tasks'] = []
#         employee_data['skills'] = []

#         # Fetch tasks assigned to this employee
#         tasks_cursor = mongo.db.task_completion_history.find({"task_assignee": employee_id})

#         for task_doc in tasks_cursor:
#             task_data = serialize_doc(task_doc)
#             task_data = process_fields(task_data)
#             employee_data['tasks'].append(task_data)

#         # Update and fetch skills
#         skills_list = update_staff_skills(employee_id)
#         print("Skills list from update_staff_skills:", skills_list)  # Log the skills list

#         # Format skills for response
#         employee_data['skills'] = skills_list  # skills_list is already a list of dicts with 'skill', 'category', 'count'
#         print('Final employee data:', employee_data)

#         session['employee_data'] = employee_data

#         return jsonify(employee_data)
#     except Exception as e:
#         print(f"Error in get_employee_data: {str(e)}")
#         return jsonify({"error": "Internal server error"}), 500

# def generate_heatmap_data(tasks):
#     """
#     Generate heatmap data from task progress.

#     Args:
#         tasks (list): List of tasks with progress data.

#     Returns:
#         list: Heatmap data in the format required by the calendar heatmap library.
#     """
#     heatmap_data = []

#     for task in tasks:
#         if task.get('progress') and isinstance(task['progress'], list):
#             for progress in task['progress']:
#                 if progress.get('start_datetime') and progress.get('end_datetime'):
#                     try:
#                         start_date = datetime.strptime(progress['start_datetime'], "%Y-%m-%dT%H:%M:%S")
#                         end_date = datetime.strptime(progress['end_datetime'], "%Y-%m-%dT%H:%M:%S")
#                         duration_hours = (end_date - start_date).total_seconds() / 3600

#                         heatmap_data.append({
#                             "date": start_date.strftime("%Y-%m-%d"),
#                             "value": duration_hours,
#                             "task_title": task.get('task_title', 'Unnamed Task')
#                         })
#                     except Exception as e:
#                         print(f"Error parsing progress dates: {str(e)}")

#     return heatmap_data

@app.route('/get_employee_data/<employee_id>', methods=['GET'])
def get_employee_data(employee_id):
    try:
        # Fetch the employee profile
        employee_profile = mongo.db.profiles.find_one({"staff_id": employee_id})
        if not employee_profile:
            return jsonify({"error": "Employee not found"}), 404

        # Convert MongoDB document to a serializable dict, excluding '_id'
        employee_data = {k: v for k, v in employee_profile.items() if k != '_id'}
        employee_data['tasks'] = []
        employee_data['skills'] = []

        # Fetch tasks assigned to this employee
        tasks_cursor = mongo.db.task_completion_history.find({"task_assignee": employee_id})

        for task_doc in tasks_cursor:
            task_data = serialize_doc(task_doc)
            task_data = process_fields(task_data)
            employee_data['tasks'].append(task_data)

        # Update and fetch skills
        skills_list = update_staff_skills(employee_id)
        print("Skills list from update_staff_skills:", skills_list)  # Log the skills list

        # Format skills for response
        employee_data['skills'] = skills_list  # skills_list is already a list of dicts with 'skill', 'category', 'count'
        print('Final employee data:', employee_data)

        # Add heatmap data to the response
        employee_data['heatmap_data'] = generate_heatmap_data(employee_data['tasks'])

        session['employee_data'] = employee_data

        return jsonify(employee_data)
    except Exception as e:
        print(f"Error in get_employee_data: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500



def rank_employees_for_task(task_description, staff_data):
    """
    Function to evaluate staff suitability for a given task based on task history and staff profiles.

    Parameters:
        task_description (str): The description of the task.
        skills (list): A list of staff skills from the database.
        staff_data (dataframe): A dataframe of staff profiles from the database.

    Returns:
        dict: A JSON-like dictionary containing staff ranks and reasoning.
    """
    # Prepare the prompt content for the AI model
    prompt_content = {
        "type": "text",
        "text": f"""
        For the task description below, compare the skills and correlating staff qualifications and rank the 3 employees best fit to complete the task based on staff competency, innovation, fairness, and availability. Provide your response in JSON format with the staff ID, match expressed as a percentage, with the highest percentage going to the most qualified, and a summary of your assessment or reasoning for that choice. Consider previous assignments for similar tasks and use the principles of fairness and inclusion to ensure equal opportunity in your evaluation.

        Task description: '{task_description}'
        Staff data: {staff_data}
        """
    }

    # Call the OpenAI API to get the staff ranking
    response = openai_client.chat.completions.create(
        model="o1-preview-2024-09-12",
        messages=[
            {
                "role": "user",
                "content": [prompt_content]
            }
        ]
    )

    # Return the response from the AI model
    return response.choices[0].message.content

rank_hierarchy = {
    "Director": 1,
    "Deputy Director": 2,
    "Chief Manager": 3,
    "Principal Manager": 4,
    "Senior Manager": 5,
    "Manager": 6,
    "Deputy Manager": 7,
    "Assistant Manager": 8,
    "Officer": 9
}

# Extract keywords using GPT4o-mini
def extract_keywords(task_title, task_description):
    """Extract the top key skills or domain keywords from the task title and description."""

    # try:
    gpt_response = openai_client.chat.completions.create(
    model="gpt-4o-mini",
    messages=[
{
    "role": "system",
    "content": [
    {
        "type": "text",
        "text": "You're an experienced HR administrator, with a deep understanding of task assignments and classifications. "
    }
    ]
},
        {
        "role": "user",
        "content": [
            {
            "type": "text",
            "text": f"Title: {task_title} \nDescription: {task_description}"
            }
        ]
        },
{
    "role": "user",
    "content": [
    {
        "type": "text",
        "text": "Extract the top key skills or domain keywords from the provided task information. return a JSON object with the keywords as a Python list of short strings, e.g.,{'domains': [\"Excel\", \"Procurement\"]}\n\ntask_title: Software Upgrade\ntask_description: Upgrade the internal CRM software to the latest version.\n\n\n\r\n"
    }
    ]
},
    ],
response_format={
"type": "json_object"
},
temperature=1,
top_p=1,
frequency_penalty=0,
presence_penalty=0
    )
    extracted_keywords = gpt_response.choices[0].message.content

    extracted_keywords = json.loads(extracted_keywords)

    print('results type & results', type(extracted_keywords), extracted_keywords)


    if not isinstance(extracted_keywords['domains'], list):
        extracted_keywords = []
        print('results', extracted_keywords)
    # except Exception as e:
    #     print("Error calling GPT4o-mini:", e)
    #     extracted_keywords = []

    # Ensure lowercasing so we can match staff skills consistently
    extracted_keywords = [kw.lower() for kw in extracted_keywords["domains"]]
    return extracted_keywords

def get_contextual_match(skill, extracted_keywords, temperature=0.1):
    """Use GPT-4 to determine if a skill contextually matches any of the extracted keywords."""
    prompt = f"Does the following skill '{skill}' contextually match or relate to any of these keywords: {extracted_keywords}? Respond with 'yes' or 'no' and a short justification for your choice."

    response = openai_client.chat.completions.create(
        model="gpt-4",
        messages=[
{
    "role": "system",
    "content": [
    {
        "type": "text",
        "text": "You're a helpful assistant."
    }
    ]
},
{'role': 'user',
     "content": [
    {
        "type": "text",
        "text": prompt,
    }
    ]},
            ],
        max_tokens=50,
        temperature=temperature
    )

    answer = response.choices[0].message.content
    # print(answer)
    return "yes" in answer.lower()

def shortlist_staff(extracted_keywords):
    """Gather all skill documents and use GPT-4 to see if they contextually match the extracted keywords."""
    potential_candidates = []
    all_skill_docs = list(mongo.db.skills.find({}))

    for skill_doc in all_skill_docs:
        staff_id = skill_doc.get("staff_id")
        skill_entries = skill_doc.get("skills", [])

        matched_count = 0
        for entry in skill_entries:
            skill_name = entry.get("skill", "").lower()

            # Use GPT-4 for contextual matching
            if get_contextual_match(skill_name, extracted_keywords):
                matched_count += 1
                break  # Avoid double-counting one skill for multiple keywords

        if matched_count > 0:
            profile = mongo.db.profiles.find_one({"staff_id": staff_id})
            if profile:
                qualifications_str = profile.get("qualifications", "").lower()

                # Check qualification match using GPT-4
                if get_contextual_match(qualifications_str, extracted_keywords):
                    potential_candidates.append(staff_id)

    potential_candidates = list(set(potential_candidates))  # Remove duplicates if any
    return potential_candidates

# Filter out staff who are "unavailable" due to overlapping tasks
def filter_unavailable_staff(potential_candidates, new_task_due_date):
    """Look for tasks in 'task_completion_history' that might overlap with this new task."""
    available_staff = []
    for staff_id in potential_candidates:
        overlapping_tasks = list(mongo.db.task_completion_history.find({
            "task_assignee": staff_id,
            "expected_completion_date": {"$gte": new_task_due_date}
        }))
        if len(overlapping_tasks) == 0:
            available_staff.append(staff_id)
    return available_staff

# Use openAI's o1 model to pick the top 3 best-fit employees
def get_best_fit_staff(available_staff, task_title, task_description, extracted_keywords, new_task_due_date):
    """Gather relevant profile/skills info to pass to the model, so it can reason about best fit."""
    staff_data_list = []
    for sid in available_staff:
        prof = mongo.db.profiles.find_one({"staff_id": sid})
        staff_name = f"{prof.get('first_name', '')} {prof.get('last_name', '')}" if prof else sid
        skdoc = mongo.db.skills.find_one({"staff_id": sid})
        skill_entries = skdoc.get("skills", []) if skdoc else []
        staff_data_list.append({
            "staff_id": sid,
            "name": staff_name,
            "qualifications": prof.get("qualifications", "") if prof else "",
            "experience": prof.get("experience", "") if prof else "",
            "skills": [s.get("skill", "") for s in skill_entries]
        })

    best_fit_prompt = f"""
        We have a new task:
        Title: {task_title}
        Description: {task_description}
        Extracted keywords: {extracted_keywords}
        Task Due Date: {new_task_due_date}

        Below is a list of staff who are available.
        Each entry: staff_id, qualifications, experience, skills.

        Staff List:
        {json.dumps(staff_data_list, indent=2)}

        Please return the 3 employees who would best fit this task, considering skill alignment,
        qualifications, and experience. If fewer than 3 staff are suitable, return as many as are suitable.
        Please provide your response in JSON format with the staff ID, match expressed as a percentage,
        with the highest percentage going to the most qualified, and a summary of your assessment or reasoning for that choice.
        Format: [{{"staff_id": "ID1", "match": 95, "reasoning": "Reason1"}}, ...}}]
        Consider previous assignments for similar tasks and use the principles of fairness and inclusion to ensure equal opportunity in your evaluation.
        """
    # try:
        # Call the OpenAI API to get the staff ranking
    response = openai_client.chat.completions.create(
    model="o1-preview",
    messages=[
            {
                "role": "user",
            "content": [
                {
                "type": "text",
                "text": best_fit_prompt,
                },
            ]
            }
        ]
    )
    # Return the response from the AI model

    raw_text =  response.choices[0].message.content
    print('O1 RESPONSE: ', raw_text)
    raw_text = raw_text.replace("```json", "").replace("```", "").strip()
    top_candidates = ast.literal_eval(raw_text)

    # top_candidates = json.loads(raw_text)
    if not isinstance(top_candidates, list):
        top_candidates = []
    # except Exception as e:
    #     print("Error calling openAI o1 model:", e)
        # top_candidates = []

    return top_candidates[:3]

# Main function
def find_best_fit_staff(task_title, task_description, new_task_due_date):
    """Find the best fit staff for a given task."""
    extracted_keywords = extract_keywords(task_title, task_description)
    print('extracted words', extracted_keywords)
    potential_candidates = shortlist_staff(extracted_keywords)
    print('potential candidates', potential_candidates)
    available_staff = filter_unavailable_staff(potential_candidates, new_task_due_date)
    print('available staff', available_staff)
    if not available_staff:
        # Return an empty list for best_fit, but still return the extracted_keywords
        return [], extracted_keywords

    best_fit = get_best_fit_staff(available_staff, task_title, task_description, extracted_keywords, new_task_due_date)
    print(best_fit)
    return best_fit, extracted_keywords


def generateTaskID():
    """Generate a unique task ID."""
    return str(uuid.uuid4())[:8]




# function to create a notification
def create_notification(staff_id, message, notification_type):
    """
    Create a notification for a staff member.

    Args:
        staff_id (str): The ID of the staff member.
        message (str): The notification message.
        notification_type (str): The type of notification (e.g., "recommendation", "assignment").

    Returns:
        dict: The created notification document.
    """
    notification = {
        "staff_id": staff_id,
        "message": message,
        "type": notification_type,
        "created_at": datetime.now(),
        "read": False  # Mark as unread by default
    }

    # Insert the notification into the database
    mongo.db.notifications.insert_one(notification)
    return notification




@app.route('/assign_task', methods=['POST'])
def assign_task():
    """
    Flask route to process task assignment requests and rank the best-fit employees.
    Handles both assigned tasks and self-generated tasks.
    """
    try:
        # Extract data from the frontend request
        request_data = request.json
        requester_id = session.get('staff_id')
        task_title = request_data.get('task_title')
        task_description = request_data.get('task_description')
        expected_completion_date = request_data.get('expected_completion_date')
        request_type = request_data.get('request_type')  # 'assigned' or 'self-generated'
        associated_objective = request_data.get('associated_objective')

        # Get KPAs from request
        kpas = request_data.get('kpas', [])

        # Convert KPAs into the progress format required by the system
        progress = [
            {
                "start_datetime": "",
                "end_datetime": "",
                "description": {
                    "text": kpa.get("text", ""),
                    "date": kpa.get("date", "")  # KPA deadline
                },
                "drafts": [],  # Initialize empty drafts array
                "status": "Not Started"  # Initial status
            }
            for kpa in kpas
        ]

        if not requester_id or not task_title or not task_description or not request_type:
            return jsonify({"error": "Missing required fields"}), 400

        # Fetch the requester's profile
        requester_profile = mongo.db.profiles.find_one({"staff_id": requester_id})
        if not requester_profile:
            return jsonify({"error": "Requester not found"}), 404

        # For self-generated tasks, the requester is initially the assignee
        task_assignee = requester_id if request_type == 'self-generated' else None

        # Get the associated objective document
        objective = None
        if associated_objective:
            objective = mongo.db.objectives.find_one({"_id": ObjectId(associated_objective)})
            if not objective:
                return jsonify({"error": "Associated objective not found"}), 404

        # Find best-fit staff only for self-generated tasks with team
        best_fit_staff = []
        tags = []

        # If participants > 1 for self-generated tasks, find best-fit staff
        if request_type == 'self-generated' and int(request_data.get('participants', 1)) > 1:
            best_fit_staff, tags = find_best_fit_staff(task_title, task_description, expected_completion_date)

            # Save recommendations to session for later use
            session['best_fit_staff'] = best_fit_staff

            # Format recommendations
            recommendations = {
                item["staff_id"]: int(item["match"]) / 100
                for item in best_fit_staff
            }
        else:
            # For assigned tasks, use the regular find_best_fit_staff logic
            best_fit_staff, tags = find_best_fit_staff(task_title, task_description, expected_completion_date)
            recommendations = {
                item["staff_id"]: int(item["match"]) / 100
                for item in best_fit_staff
            }

        # Generate task ID and create task document
        tid = generateTaskID()
        new_task = {
            "task_id": tid,
            "task_title": task_title,
            "task_description": task_description,
            "expected_completion_date": expected_completion_date,
            "requester_id": requester_id,
            "request_type": request_type,
            "associated_objective": str(objective["_id"]) if objective else None,
            "tags": tags,
            "recommendations": recommendations,
            "progress": progress,
            "status": "pending" if request_type == "assigned" else "draft",  # Self-generated tasks start as drafts
            "created_at": datetime.now(),
            "assignment_date": datetime.now() if request_type == "self-generated" else None,
            "completion_date": None,
            "task_assignee": task_assignee,  # Set initial assignee for self-generated
            "team_members": [requester_id] if request_type == "self-generated" else [],  # Initial team for self-generated
            "approval_status": "pending" if request_type == "self-generated" else "approved"  # Self-generated need approval
        }

        # Insert the task into the database
        mongo.db.task_completion_history.insert_one(new_task)

        # Save task_id to session for reference
        session['task_id'] = tid

        # For self-generated tasks that need additional team members, return rankings
        if request_type == 'self-generated' and int(request_data.get('participants', 1)) > 1:
            return jsonify({"ranking": best_fit_staff, "task_id": tid}), 200

        # For self-generated tasks, notify the approver (unit head or division head)
        if request_type == 'self-generated':
            # Get the requester's unit head or division head
            approver_id = None

            if requester_profile.get('status') == 'Unit_member':
                # Find unit head of the same division
                approver = mongo.db.profiles.find_one({
                    "division": requester_profile.get('division'),
                    "status": "Unit_head"
                })
                if approver:
                    approver_id = approver.get('staff_id')
            elif requester_profile.get('status') == 'Unit_head':
                # Find division head
                approver = mongo.db.profiles.find_one({
                    "division": requester_profile.get('division'),
                    "status": "Division_head"
                })
                if approver:
                    approver_id = approver.get('staff_id')

            if approver_id:
                # Create notification for approver
                message = f"New self-generated task '{task_title}' requires your approval."
                create_notification(approver_id, message, "approval_request")

        # For assigned tasks, return best_fit_staff for the assigner to choose
        if request_type == 'assigned':
            return jsonify({"ranking": best_fit_staff}), 200
        else:
            return jsonify({"message": "Self-generated task created successfully", "task_id": tid}), 200

    except Exception as e:
        print(f"Error in assign_task: {str(e)}")
        return jsonify({"error": str(e)}), 500



@app.route('/invite_team_members', methods=['POST'])
def invite_team_members():
    try:
        if 'staff_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        requester_id = session.get('staff_id')
        task_id = session.get('task_id')

        if not task_id:
            return jsonify({"error": "No active task found"}), 400

        # Get selected team members from request
        data = request.json
        selected_members = data.get('selected_members', [])

        if not selected_members:
            return jsonify({"error": "No team members selected"}), 400

        # Get the task
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})

        if not task:
            return jsonify({"error": "Task not found"}), 404

        # Verify that the requester is the task creator
        if task.get('requester_id') != requester_id:
            return jsonify({"error": "Unauthorized: You are not the task creator"}), 403

        # Add selected members to the team_members array
        mongo.db.task_completion_history.update_one(
            {"task_id": task_id},
            {"$addToSet": {"team_members": {"$each": selected_members}}}
        )

        # Create notifications for invited team members
        for member_id in selected_members:
            message = f"You have been invited to join the self-generated task '{task.get('task_title')}'."
            create_notification(member_id, message, "team_invitation")

        return jsonify({"message": "Team members invited successfully"}), 200

    except Exception as e:
        print(f"Error in invite_team_members: {str(e)}")
        return jsonify({"error": str(e)}), 500



# Route for responding to a team invitation
@app.route('/respond_to_invitation', methods=['POST'])
def respond_to_invitation():
    try:
        if 'staff_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        staff_id = session.get('staff_id')
        data = request.json
        task_id = data.get('task_id')
        response = data.get('response')  # 'accept' or 'decline'

        if not task_id or not response:
            return jsonify({"error": "Missing required fields"}), 400

        # Get the task
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})

        if not task:
            return jsonify({"error": "Task not found"}), 404

        # Check if staff is in the invited team members
        if staff_id not in task.get('team_members', []):
            return jsonify({"error": "You were not invited to this task"}), 403

        if response == 'accept':
            # Add staff to confirmed team members
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {"$addToSet": {"confirmed_team_members": staff_id}}
            )

            # Notify task creator
            message = f"{staff_id} has accepted your invitation to join the task '{task.get('task_title')}'."
            create_notification(task.get('requester_id'), message, "invitation_accepted")

            return jsonify({"message": "Invitation accepted"}), 200
        elif response == 'decline':
            # Remove staff from team members
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {"$pull": {"team_members": staff_id}}
            )

            # Notify task creator
            message = f"{staff_id} has declined your invitation to join the task '{task.get('task_title')}'."
            create_notification(task.get('requester_id'), message, "invitation_declined")

            return jsonify({"message": "Invitation declined"}), 200
        else:
            return jsonify({"error": "Invalid response. Must be 'accept' or 'decline'"}), 400

    except Exception as e:
        print(f"Error in respond_to_invitation: {str(e)}")
        return jsonify({"error": str(e)}), 500




# Route for approving or rejecting a self-generated task
@app.route('/approve_task', methods=['POST'])
def approve_task():
    try:
        if 'staff_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        approver_id = session.get('staff_id')
        data = request.json
        task_id = data.get('task_id')
        decision = data.get('decision')  # 'approve' or 'reject'
        comments = data.get('comments', '')

        if not task_id or not decision:
            return jsonify({"error": "Missing required fields"}), 400

        # Get the task
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})

        if not task:
            return jsonify({"error": "Task not found"}), 404

        # Get the approver's profile to check if they're authorized
        approver_profile = mongo.db.profiles.find_one({"staff_id": approver_id})

        if not approver_profile:
            return jsonify({"error": "Approver not found"}), 404

        # Get the requester's profile
        requester_profile = mongo.db.profiles.find_one({"staff_id": task.get('requester_id')})

        if not requester_profile:
            return jsonify({"error": "Requester not found"}), 404

        # Check if approver is authorized (Unit head for unit members, Division head for unit heads)
        is_authorized = False

        if requester_profile.get('status') == 'Unit_member' and approver_profile.get('status') == 'Unit_head':
            is_authorized = approver_profile.get('division') == requester_profile.get('division')
        elif requester_profile.get('status') == 'Unit_head' and approver_profile.get('status') == 'Division_head':
            is_authorized = approver_profile.get('division') == requester_profile.get('division')

        if not is_authorized:
            return jsonify({"error": "Unauthorized: You are not authorized to approve this task"}), 403

        if decision == 'approve':
            # Update task status to active
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "approval_status": "approved",
                        "status": "active",
                        "approval_date": datetime.now(),
                        "approver_id": approver_id,
                        "approval_comments": comments
                    }
                }
            )

            # Notify task creator and team members
            message = f"Your self-generated task '{task.get('task_title')}' has been approved."
            create_notification(task.get('requester_id'), message, "task_approved")

            for member_id in task.get('confirmed_team_members', []):
                if member_id != task.get('requester_id'):
                    message = f"The task '{task.get('task_title')}' you're part of has been approved."
                    create_notification(member_id, message, "task_approved")

            return jsonify({"message": "Task approved successfully"}), 200
        elif decision == 'reject':
            # Update task status to rejected
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {
                    "$set": {
                        "approval_status": "rejected",
                        "status": "rejected",
                        "approval_date": datetime.now(),
                        "approver_id": approver_id,
                        "rejection_reason": comments
                    }
                }
            )

            # Notify task creator
            message = f"Your self-generated task '{task.get('task_title')}' has been rejected."
            create_notification(task.get('requester_id'), message, "task_rejected")

            # Notify team members
            for member_id in task.get('confirmed_team_members', []):
                if member_id != task.get('requester_id'):
                    message = f"The task '{task.get('task_title')}' you were invited to has been rejected."
                    create_notification(member_id, message, "task_rejected")

            return jsonify({"message": "Task rejected successfully"}), 200
        else:
            return jsonify({"error": "Invalid decision. Must be 'approve' or 'reject'"}), 400

    except Exception as e:
        print(f"Error in approve_task: {str(e)}")
        return jsonify({"error": str(e)}), 500




# Route to get self-generated tasks for approval
@app.route('/get_tasks_for_approval')
def get_tasks_for_approval():
    try:
        if 'staff_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        approver_id = session.get('staff_id')

        # Get the approver's profile
        approver_profile = mongo.db.profiles.find_one({"staff_id": approver_id})

        if not approver_profile:
            return jsonify({"error": "Approver not found"}), 404

        # Get tasks for approval based on approver's role
        query = {
            "request_type": "self-generated",
            "approval_status": "pending"
        }

        # For Unit head, get tasks from unit members in their division
        if approver_profile.get('status') == 'Unit_head':
            # Find all unit members in the approver's division
            unit_members = list(mongo.db.profiles.find(
                {
                    "division": approver_profile.get('division'),
                    "status": "Unit_member"
                },
                {"staff_id": 1}
            ))

            unit_member_ids = [member.get('staff_id') for member in unit_members]

            query["requester_id"] = {"$in": unit_member_ids}

        # For Division head, get tasks from unit heads in their division
        elif approver_profile.get('status') == 'Division_head':
            # Find all unit heads in the approver's division
            unit_heads = list(mongo.db.profiles.find(
                {
                    "division": approver_profile.get('division'),
                    "status": "Unit_head"
                },
                {"staff_id": 1}
            ))

            unit_head_ids = [head.get('staff_id') for head in unit_heads]

            query["requester_id"] = {"$in": unit_head_ids}

        # Get tasks that match the query
        tasks = list(mongo.db.task_completion_history.find(query))

        # Process tasks for response
        processed_tasks = []
        for task in tasks:
            # Convert ObjectId to string
            task['_id'] = str(task['_id'])

            # Add requester name
            requester = mongo.db.profiles.find_one({"staff_id": task.get('requester_id')})
            if requester:
                task['requester_name'] = f"{requester.get('first_name', '')} {requester.get('last_name', '')}"

            # Add objective details
            if task.get('associated_objective'):
                objective = mongo.db.objectives.find_one({"_id": ObjectId(task.get('associated_objective'))})
                if objective:
                    task['objective_title'] = objective.get('title')

            processed_tasks.append(task)

        return jsonify({"tasks": processed_tasks}), 200

    except Exception as e:
        print(f"Error in get_tasks_for_approval: {str(e)}")
        return jsonify({"error": str(e)}), 500



# Route to get a user's self-generated tasks
@app.route('/get_self_generated_tasks')
def get_self_generated_tasks():
    try:
        if 'staff_id' not in session:
            return jsonify({"error": "Unauthorized"}), 401

        staff_id = session.get('staff_id')

        # Get tasks that the user has created
        created_tasks = list(mongo.db.task_completion_history.find({
            "requester_id": staff_id,
            "request_type": "self-generated"
        }))

        # Get tasks that the user is a team member of
        team_tasks = list(mongo.db.task_completion_history.find({
            "team_members": staff_id,
            "requester_id": {"$ne": staff_id},
            "request_type": "self-generated"
        }))

        # Process tasks for response
        processed_created_tasks = []
        for task in created_tasks:
            # Convert ObjectId to string
            task['_id'] = str(task['_id'])

            # Add objective details
            if task.get('associated_objective'):
                objective = mongo.db.objectives.find_one({"_id": ObjectId(task.get('associated_objective'))})
                if objective:
                    task['objective_title'] = objective.get('title')

            processed_created_tasks.append(task)

        processed_team_tasks = []
        for task in team_tasks:
            # Convert ObjectId to string
            task['_id'] = str(task['_id'])

            # Add requester name
            requester = mongo.db.profiles.find_one({"staff_id": task.get('requester_id')})
            if requester:
                task['requester_name'] = f"{requester.get('first_name', '')} {requester.get('last_name', '')}"

            # Add objective details
            if task.get('associated_objective'):
                objective = mongo.db.objectives.find_one({"_id": ObjectId(task.get('associated_objective'))})
                if objective:
                    task['objective_title'] = objective.get('title')

            processed_team_tasks.append(task)

        return jsonify({
            "created_tasks": processed_created_tasks,
            "team_tasks": processed_team_tasks
        }), 200

    except Exception as e:
        print(f"Error in get_self_generated_tasks: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route('/confirm_assignment', methods=['POST'])
def submitSelectedStaff():
    try:
        selected_staff = request.json.get('selected_staff')
        task_id = session.get('task_id', "")
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})

        if not task:
            return jsonify({"error": "Task not found"}), 404

        # Extract staff ID (e.g., "E001" from "E001_John Doe")
        task_assignee = selected_staff[0].split("_")[0]

        # Update task with assignee and timestamps
        mongo.db.task_completion_history.update_one(
            {"task_id": task_id},
            {
                "$set": {
                    "task_assignee": task_assignee,
                    "assignment_date": datetime.utcnow(),
                    "completion_date": None
                }
            }
        )

        # Notify the assigned staff
        for staff_entry in selected_staff:
            staff_id = staff_entry.split("_")[0]
            message = f"You have been assigned to the task '{task['task_title']}'."
            create_notification(staff_id, message, "assignment")

        return jsonify({"message": "Selected staff submitted successfully"}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500




@app.route('/get_notifications', methods=['GET'])
def get_notifications():
    try:
        staff_id = session.get('staff_id')
        notifications = list(mongo.db.notifications.find({"staff_id": staff_id}).sort("created_at", -1))
        # Convert ObjectId to string for JSON serialization
        for notification in notifications:
            notification["_id"] = str(notification["_id"])
        return jsonify(notifications)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/api/notifications/mark-as-read/<notification_id>', methods=['PUT'])
def mark_notification_as_read(notification_id):
    try:
        mongo.db.notifications.update_one(
            {"_id": ObjectId(notification_id)},
            {"$set": {"read": True}}
        )
        return jsonify({"message": "Notification marked as read"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500





def generate_heatmap_data(tasks):
    """
    Generate heatmap data from task progress.

    Args:
        tasks (list): List of tasks with progress data.

    Returns:
        list: Heatmap data in the format required by the calendar heatmap library.
    """
    heatmap_data = []

    for task in tasks:
        if task.get('progress') and isinstance(task['progress'], list):
            for progress in task['progress']:
                # Check if the progress item has valid start and end datetimes
                if progress.get('start_datetime') and progress.get('end_datetime'):
                    try:
                        # Parse start and end datetimes
                        start_date = datetime.strptime(progress['start_datetime'], "%Y-%m-%dT%H:%M:%SZ")
                        end_date = datetime.strptime(progress['end_datetime'], "%Y-%m-%dT%H:%M:%SZ")
                        duration_hours = (end_date - start_date).total_seconds() / 3600

                        # Add heatmap data for this progress item
                        heatmap_data.append({
                            "date": start_date.strftime("%Y-%m-%d"),
                            "value": duration_hours,
                            "task_title": task.get('task_title', 'Unnamed Task'),
                            "description": progress.get('description', {}).get('text', 'No description')
                        })
                    except Exception as e:
                        print(f"Error parsing progress dates: {str(e)}")

    return heatmap_data

# @app.route('/get_employee_data', methods=['GET'])
# def get_employee_data():
#     try:
#         employee_id = session.get('staff_id')
#         # Fetch the employee profile
#         employee_profile = mongo.db.profiles.find_one({"staff_id": employee_id})
#         if not employee_profile:
#             return jsonify({"error": "Employee not found"}), 404

#         # Convert MongoDB document to a serializable dict, excluding '_id'
#         employee_data = {k: v for k, v in employee_profile.items() if k != '_id'}
#         employee_data['tasks'] = []
#         employee_data['skills'] = []

#         # Fetch tasks assigned to this employee
#         tasks_cursor = mongo.db.task_completion_history.find({"task_assignee": employee_id})

#         # Fetch tasks created by this employee
#         created_tasks_cursor = mongo.db.task_completion_history.find({"requester_id": employee_id})

#         for task_doc in tasks_cursor:
#             task_data = serialize_doc(task_doc)
#             task_data = process_fields(task_data)
#             employee_data['tasks'].append(task_data)

#         for task_doc in created_tasks_cursor:
#             created_task_data = serialize_doc(task_doc)
#             created_task_data = process_fields(created_task_data)
#             employee_data['created_tasks'].append(created_task_data)

#         # Update and fetch skills
#         skills_list = update_staff_skills(employee_id)
#         print("Skills list from update_staff_skills:", skills_list)  # Log the skills list

#         # Format skills for response
#         employee_data['skills'] = skills_list  # skills_list is already a list of dicts with 'skill', 'category', 'count'
#         print('Final employee data:', employee_data)

#         # Add heatmap data to the response
#         employee_data['heatmap_data'] = generate_heatmap_data(employee_data['tasks'])

#         session['employee_data'] = employee_data

#         return jsonify(employee_data)
#     except Exception as e:
#         print(f"Error in get_employee_data: {str(e)}")
#         return jsonify({"error": "Internal server error"}), 500





@app.route('/get_task', methods=['GET'])
def get_task():
    # try:
    task_id = request.args.get('task_id')
    task = mongo.db.task_completion_history.find_one({"task_id": task_id})
    if not task:
        return jsonify({"error": "Task not found"}), 404

    # Convert MongoDB document to JSON-safe format
    task['_id'] = str(task['_id'])

    for progress in task.get('progress', []):
        # Convert description date
        if 'description' in progress and 'date' in progress['description']:
            if isinstance(progress['description']['date'], str):
                progress['description']['date'] = datetime.strptime(
                    progress['description']['date'], "%Y-%m-%d"
                ).isoformat()

        # Process drafts
        for draft in progress.get('drafts', []):
            # Handle submitted_at
            if isinstance(draft['submitted_at'], str):
                draft['submitted_at'] = datetime.strptime(
                    draft['submitted_at'], "%Y-%m-%dT%H:%M:%SZ"
                ).isoformat()

            else:
                draft['submitted_at'] = draft['submitted_at'].isoformat()

            # Handle reviewed_at if exists
            if 'reviewed_at' in draft:
                if isinstance(draft['reviewed_at'], str):
                    draft['reviewed_at'] = datetime.strptime(
                        draft['reviewed_at'], "%Y-%m-%dT%H:%M:%SZ"
                    ).isoformat()
                else:
                    draft['reviewed_at'] = draft['reviewed_at'].isoformat()

    return jsonify(task), 200
    # except Exception as e:
    #     return jsonify({"error": str(e)}), 500
# Draft submission
@app.route('/tasks/<task_id>/progress/<progress_index>/submit-draft', methods=['POST'])
def submit_draft(task_id, progress_index):
    try:
        data = request.json
        draft_content = data.get('content')
        # staff_id = data.get('staff_id')

        # Get requester_id from task_completion_history collection
        requester_id = mongo.db.task_completion_history.find_one({"task_id": task_id})['requester_id']

        # Add draft to the progress item. Ensure dates are stored as datetime objects
        mongo.db.task_completion_history.update_one(
            {"task_id": task_id},
            {
                "$push": {
                    f"progress.{progress_index}.drafts": {
                        "draft_id": str(ObjectId()),
                        "content": draft_content,
                        "submitted_at": datetime.now(),
                        "status": "Submitted"
                    }
                },
                "$set": {
                    f"progress.{progress_index}.status": "Under Review",
                    "updated_at": datetime.now()
                }
            }
        )

        # Notify supervisor/requestor
        create_notification(requester_id, f'A draft for item {progress_index} of task {task_id} has been submitted for your review.', 'draft submission')

        return jsonify({"message": "Draft submitted successfully"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# Review draft
@app.route('/tasks/<task_id>/progress/<progress_index>/review-draft', methods=['POST'])
def review_draft(task_id, progress_index):
    try:
        data = request.json
        review_status = data.get('status')  # "Approved" or "Revisions Requested"
        comments = data.get('comments', [])
        reviewer_id = data.get('reviewer_id')

        # Update draft status and add comments
        mongo.db.task_completion_history.update_one(
            {"task_id": task_id},
            {
                "$set": {
                    f"progress.{progress_index}.drafts.$[draft].status": review_status,
                    f"progress.{progress_index}.status": review_status,
                    f"progress.{progress_index}.drafts.$[draft].reviewed_by": reviewer_id,
                    f"progress.{progress_index}.drafts.$[draft].reviewed_at": datetime.now(),
                    f"progress.{progress_index}.drafts.$[draft].comments": comments,
                    "updated_at": datetime.now()
                }
            },
            array_filters=[{"draft.status": "Submitted"}]
        )

        # Notify task assignee
        create_notification(reviewer_id, f'Item {progress_index} of task {task_id} has been reviewed. Please check the comments.', 'draft review')

        return jsonify({"message": "Draft reviewed successfully"}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# Close task
@app.route('/tasks/<task_id>/close', methods=['POST'])
def close_task(task_id):
    try:
        # Check if all progress items are approved
        task = mongo.db.task_completion_history.find_one({"task_id": task_id})
        if all(progress.get('status') == 'Approved' for progress in task['progress']):
            mongo.db.task_completion_history.update_one(
                {"task_id": task_id},
                {"$set": {"status": "Completed", "completion_date": datetime.now()}}
            )
            return jsonify({"message": "Task closed successfully"}), 200
        else:
            return jsonify({"error": "Not all KPAs are approved"}), 400
    except Exception as e:
        return jsonify({"error": str(e)}), 500





if __name__ == '__main__':
    app.run(debug=True)
