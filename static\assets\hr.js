// =================================================================================
// #region INITIALIZATION
// This is the single entry point for all JavaScript functionality on the page.
// =================================================================================

document.addEventListener('DOMContentLoaded', function() {
    // --- Initialize UI Components ---
    initializeTabs();
    initializeKpaButtons();
    initializeTaskForms();
    initializeButtonRowNavigation();
    initializeModals();
    initializeSearch();
    initializeObjectiveManagement();
    initializeTaskApprovalButton();

    // --- Initial Data Fetching ---
    fetchCurrentUser(); // Fetches user ID first
    fetchAndBuildObjectivesTree(); // Fetches data for the D3 tree
    fetchRegularObjectives(); // Populates the "Assigned Task" objective dropdown
    fetchSelfGeneratedObjectives(); // Populates the "Self-Generated" objective dropdown
    fetchNotifications();

    // --- Load Default View ---
    loadSectionContent('user-profile');
});

// #endregion

// =================================================================================
// #region INITIALIZATION FUNCTIONS
// These functions set up the primary event listeners for the page.
// =================================================================================

/**
 * Sets up the functionality for switching between "Assigned Task" and "Self-Generated Task" tabs.
 */
function initializeTabs() {
    const taskTabs = document.querySelectorAll('#task-assignment-tabs button');
    const taskContents = document.querySelectorAll('.task-content');
    if (!taskTabs.length) return;

    taskTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            taskTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            taskContents.forEach(content => content.classList.remove('active'));
            document.getElementById(tabId)?.classList.add('active');
        });
    });
    taskTabs[0].click(); // Activate the first tab by default
}

/**
 * Adds click listeners to the "Add KPA" buttons in both task forms.
 */
function initializeKpaButtons() {
    const kpaAssignments = {
        'add-kpa-btn-assign': 'kpa-container-assign',
        'add-kpa-btn-self': 'kpa-container-self'
    };
    Object.entries(kpaAssignments).forEach(([buttonId, containerId]) => {
        const button = document.getElementById(buttonId);
        const container = document.getElementById(containerId);
        if (button && container) {
            button.addEventListener('click', () => addKpaInputGroup(container));
        }
    });
}

/**
 * Attaches submit event listeners to the task creation forms.
 */
function initializeTaskForms() {
    const forms = {
        'assign-task-form': 'assigned',
        'self-generated-task-form': 'self-generated'
    };
    Object.entries(forms).forEach(([formId, requestType]) => {
        const form = document.getElementById(formId);
        form?.addEventListener('submit', e => {
            e.preventDefault();
            if (validateForm(form)) {
                submitTaskForm(form, requestType);
            }
        });
    });
}

/**
 * Sets up the main navigation buttons (e.g., "User Profile", "Objectives").
 */
function initializeButtonRowNavigation() {
    document.querySelectorAll('#button-row button').forEach(button => {
        button.addEventListener('click', function() {
            const section = this.dataset.section;
            loadSectionContent(section);
            if (section === 'objectives-tree') {
                fetchAndBuildObjectivesTree(); // Refresh the tree when navigated to
            }
        });
    });
}

/**
 * Initializes all modals with their open/close logic and form submission handlers.
 */
function initializeModals() {
    // Draft Submission Modal
    const draftModal = document.getElementById('draft-modal');
    draftModal?.addEventListener('click', e => { if (e.target === draftModal) closeDraftModal(); });
    document.getElementById('draft-close')?.addEventListener('click', closeDraftModal);
    document.getElementById('draft-form')?.addEventListener('submit', handleDraftFormSubmit);

    // Review Modal
    const reviewModal = document.getElementById('review-modal');
    reviewModal?.addEventListener('click', e => { if (e.target === reviewModal) closeReviewModal(); });
    document.getElementById('review-close')?.addEventListener('click', closeReviewModal);
    document.getElementById('review-form')?.addEventListener('submit', handleReviewFormSubmit);

    // Objective Creation Modal
    const objectiveModal = document.getElementById('objective-modal');
    objectiveModal?.addEventListener('click', e => { if (e.target === objectiveModal) objectiveModal.style.display = 'none'; });
    document.getElementById('add-objective-btn')?.addEventListener('click', () => { objectiveModal.style.display = 'block'; });
    document.querySelector('#objective-modal .close')?.addEventListener('click', () => { objectiveModal.style.display = 'none'; });
}

/**
 * Sets up the debounced search functionality for employees.
 */
function initializeSearch() {
    const searchInput = document.getElementById('employee-search-input');
    const searchResults = $('#employee-search-results');
    if (!searchInput) return;

    const debouncedSearch = debounce(function(searchTerm) {
        if (searchTerm.length < 2) {
            searchResults.empty().hide();
            return;
        }
        fetch(`/search_employees?search=${encodeURIComponent(searchTerm)}`)
            .then(response => response.json())
            .then(data => showSearch(data))
            .catch(handleError);
    }, 250);

    searchInput.addEventListener('input', (e) => debouncedSearch(e.target.value.trim()));

    searchResults.on('click', '.search-result-item', function() {
        const employeeId = $(this).data('id');
        loadEmployeeData(employeeId);
        searchInput.value = $(this).text().trim();
        searchResults.empty().hide();
    });
}

/**
 * Sets up event listeners for creating, editing, and deleting objectives.
 */
function initializeObjectiveManagement() {
    document.getElementById('create-objective-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const cardData = {
            'title': formData.get('title'),
            'description': formData.get('description'),
            'type': 'organisational'
        };
        fetch('/objectives/add', { method: 'POST', body: formData })
            .then(response => response.json())
            .then(data => {
                appendNewObjective(cardData, data.id);
                document.getElementById('objective-modal').style.display = 'none';
                this.reset();
            })
            .catch(handleError);
    });

    document.getElementById('objectives-container')?.addEventListener('click', function(event) {
        const deleteBtn = event.target.closest('.delete-btn');
        if (deleteBtn) {
            deleteObjective(deleteBtn.dataset.id);
        }
    });
}

/**
 * Dynamically adds the "Task Approval" button and sets up its click listener.
 */
function initializeTaskApprovalButton() {
    const buttonRow = document.getElementById('button-row');
    if (buttonRow && !buttonRow.querySelector('[data-section="task-approval"]')) {
        const approvalButton = document.createElement('button');
        approvalButton.dataset.section = 'task-approval';
        approvalButton.textContent = 'Task Approval';
        buttonRow.appendChild(approvalButton);

        approvalButton.addEventListener('click', function() {
            loadSectionContent('task-approval');
            loadTasksForApproval();
        });
    }
}

// #endregion

// =================================================================================
// #region CORE UI & DATA FUNCTIONS
// =================================================================================

function loadSectionContent(section) {
    document.querySelectorAll('.section-content').forEach(sec => sec.style.display = 'none');
    const sectionEl = document.getElementById(section);
    if (sectionEl) {
        sectionEl.style.display = 'block';
    }
}

/**
 * Fetches data and builds the D3 objectives tree.
 */
function fetchAndBuildObjectivesTree() {
    fetch('/objectives')
        .then(response => response.json())
        .then(data => {
            const processedData = processObjectivesData(data.objectives);
            createCollapsibleTree(processedData);
        })
        .catch(err => console.error('Error loading objectives tree:', err));
}

function fetchCurrentUser() {
    fetch('/get_current_user')
        .then(response => response.json())
        .then(data => {
            if (data.staff_id) {
                sessionStorage.setItem('staffId', data.staff_id);
                fetchTaskProgress(); // Now fetch progress since we have the ID.
                loadEmployeeData(data.staff_id); // Load initial data for logged-in user.
            }
        })
        .catch(handleError);
}

function fetchRegularObjectives() {
    fetch('/get_associated_objectives?task_type=regular')
        .then(response => response.json())
        .then(data => populateDropdown('associated_objectives', data.objectives))
        .catch(handleError);
}

function fetchSelfGeneratedObjectives() {
    fetch('/get_associated_objectives?task_type=self-generated')
        .then(response => response.json())
        .then(data => populateDropdown('self_associated_objectives', data.objectives))
        .catch(handleError);
}

function fetchTaskProgress() {
    const employeeId = sessionStorage.getItem('staffId');
    if (!employeeId) return;

    fetch(`/get_employee_data/${employeeId}`)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('task-progress-container');
            if (!container) return;
            if (!data.tasks || data.tasks.length === 0) {
                container.innerHTML = '<p>You have no assigned tasks.</p>';
                return;
            }
            container.innerHTML = data.tasks.map(task => createTaskProgressHTML(task, data.staff_id)).join('');
            container.querySelectorAll('.step').forEach(kpa => {
                kpa.addEventListener('click', (e) => {
                    if (e.target.closest('.delete-draft')) return;
                    const { taskId, progressIndex, isRequester } = kpa.dataset;
                    handleKpaClick(taskId, progressIndex, isRequester === 'true');
                });
            });
        })
        .catch(handleError);
}

function fetchNotifications() {
    fetch('/get_notifications')
        .then(response => response.json())
        .then(displayNotifications)
        .catch(handleError);
}

function loadEmployeeData(employeeId) {
    if (!employeeId) return;
    fetch(`/get_employee_data/${employeeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) throw new Error(data.error);
            if (window.calendarHeatmap) initFlaskCalendarHeatmap(data);
            if (window.RadarChart) generateRadarChart(data);
        })
        .catch(handleError);
}

// #endregion

// =================================================================================
// #region DOM MANIPULATION & DISPLAY
// =================================================================================

function addKpaInputGroup(container) {
    if (!container) return;
    const kpaInputGroup = document.createElement('div');
    kpaInputGroup.className = 'kpa-input-group';
    kpaInputGroup.innerHTML = `
        <input type="text" name="kpa_text[]" placeholder="Enter KPA" required>
        <input type="date" name="kpa_date[]" required>
        <button type="button" class="delete-kpa">&times;</button>
    `;
    container.appendChild(kpaInputGroup);
    kpaInputGroup.querySelector('.delete-kpa').addEventListener('click', () => container.removeChild(kpaInputGroup));
}

function showSearch(data) {
    const searchResults = $('#employee-search-results');
    searchResults.empty().hide();
    if (data && data.length > 0) {
        data.forEach(employee => {
            searchResults.append(`<div class="search-result-item" data-id="${employee.id}">${employee.name}</div>`);
        });
        searchResults.show();
    }
}

function populateDropdown(selectId, options) {
    const selectElement = document.getElementById(selectId);
    if (selectElement) {
        selectElement.innerHTML = '<option value="" disabled selected>Select an objective</option>';
        options.forEach(objective => {
            const option = document.createElement('option');
            option.value = objective.id;
            option.textContent = `(${objective.type}) ${objective.title}`;
            selectElement.appendChild(option);
        });
    }
}

function createTaskProgressHTML(task, currentUserId) {
    return `
        <div class="task-progress-item">
            <h4>${task.task_title}</h4>
            <div class="progress-bar-container">
                <div class="progress-bar" style="width: ${calculateProgressWidth(task.progress)}%;"></div>
            </div>
            <ul class="progress-steps">
                ${task.progress.map((kpa, index) => `
                    <li class="step ${getProgressClass(kpa.status)} ${new Date(kpa.description.date) < new Date() ? 'past-date' : ''}"
                        data-task-id="${task.task_id}"
                        data-progress-index="${index}"
                        data-is-requester="${task.requester_id === currentUserId}">
                        <label>${kpa.description.text}</label>
                        <small>Due: ${new Date(kpa.description.date).toLocaleDateString()}</small>
                    </li>
                `).join('')}
            </ul>
        </div>
    `;
}

function displayNotifications(notifications) {
    const notificationsContent = document.getElementById("notifications-content");
    if (!notificationsContent) return;
    notificationsContent.innerHTML = (!notifications || notifications.length === 0) ?
        "<p>No new notifications.</p>" :
        notifications.map(notification => `
            <div class="notification" data-id="${notification._id}">
                <p><strong>${notification.type.toUpperCase()}</strong></p>
                <p>${notification.message}</p>
                <small>${new Date(notification.created_at).toLocaleString()}</small>
            </div>
        `).join('');

    notificationsContent.querySelectorAll('.notification').forEach(el => {
        el.addEventListener('click', () => {
            markNotificationAsRead(el.dataset.id);
            el.style.opacity = "0.6";
        });
    });
}

function displayRecommendations(ranking) {
    const container = document.querySelector('.recommendations ul');
    if (!container) return;
    container.innerHTML = '';
    const oldBtn = container.parentElement.querySelector('button');
    if (oldBtn) oldBtn.remove();
    if (ranking.length === 0) {
        container.innerHTML = '<li>No suitable staff found.</li>';
        return;
    }
    ranking.forEach(staff => {
        const li = document.createElement('li');
        li.innerHTML = `
            <input type="radio" id="staff_${staff.staff_id}" name="staff_recommendation" value="${staff.staff_id}">
            <label for="staff_${staff.staff_id}">${staff.staff_id} - ${staff.match}% Match - ${staff.reasoning}</label>
        `;
        container.appendChild(li);
    });
    const submitBtn = document.createElement('button');
    submitBtn.textContent = 'Confirm & Assign Task';
    submitBtn.addEventListener('click', () => {
        const selectedRadio = document.querySelector('input[name="staff_recommendation"]:checked');
        if (selectedRadio) {
            submitSelectedStaff([selectedRadio.value]);
        } else {
            alert('Please select a staff member.');
        }
    });
    container.parentElement.appendChild(submitBtn);
}

// #endregion

// =================================================================================
// #region MODAL & WORKFLOW LOGIC
// =================================================================================

let currentTaskId = null;
let currentProgressIndex = null;

function handleKpaClick(taskId, progressIndex, isRequester) {
    if (isRequester) {
        showReviewModal(taskId, progressIndex);
    } else {
        showDraftModal(taskId, progressIndex);
    }
}

// --- Draft Modal ---
function showDraftModal(taskId, progressIndex) {
    currentTaskId = taskId;
    currentProgressIndex = progressIndex;
    document.getElementById('draft-modal').classList.add('active');
    loadExistingDrafts(taskId, progressIndex);
}

function closeDraftModal() {
    document.getElementById('draft-modal').classList.remove('active');
}

async function handleDraftFormSubmit(e) {
    e.preventDefault();
    const content = new FormData(e.target).get('draft-content');
    try {
        const response = await fetch(`/tasks/${currentTaskId}/progress/${currentProgressIndex}/submit-draft`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ content })
        });
        await handleResponse(response);
        alert('Draft submitted successfully!');
        closeDraftModal();
        fetchTaskProgress();
    } catch (error) {
        handleError(error);
    }
}

async function loadExistingDrafts(taskId, progressIndex) {
    try {
        const task = await (await fetch(`/get_task?task_id=${taskId}`)).json();
        const drafts = task.progress[progressIndex].drafts;
        const draftList = document.getElementById('draft-history');
        draftList.innerHTML = (drafts && drafts.length > 0) ?
            drafts.map(draft => `
                <div class="draft-item">
                    <p>Submitted: ${new Date(draft.submitted_at).toLocaleString()} | Status: ${draft.status}</p>
                    ${draft.comments?.length ? `<div class="comments"><strong>Comments:</strong> ${draft.comments.join(', ')}</div>` : ''}
                </div>
            `).join('') :
            '<p>No submission history for this KPA.</p>';
    } catch (error) {
        handleError(error);
    }
}

// --- Review Modal ---
function showReviewModal(taskId, progressIndex) {
    currentTaskId = taskId;
    currentProgressIndex = progressIndex;
    document.getElementById('review-modal').classList.add('active');
    loadDraftForReview(taskId, progressIndex);
}

function closeReviewModal() {
    document.getElementById('review-modal').classList.remove('active');
}

async function handleReviewFormSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const reviewData = {
        status: formData.get('review-status'),
        comments: [formData.get('review-comments')],
        reviewer_id: sessionStorage.getItem('staffId')
    };
    try {
        const response = await fetch(`/tasks/${currentTaskId}/progress/${currentProgressIndex}/review-draft`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(reviewData)
        });
        await handleResponse(response);
        alert('Review submitted successfully!');
        closeReviewModal();
        fetchTaskProgress();
    } catch (error) {
        handleError(error);
    }
}

async function loadDraftForReview(taskId, progressIndex) {
    try {
        const task = await (await fetch(`/get_task?task_id=${taskId}`)).json();
        const kpa = task.progress[progressIndex];
        const reviewContent = document.getElementById('review-content');
        reviewContent.innerHTML = `<h3>${kpa.description.text}</h3>` +
            (kpa.drafts.map(draft => `
                <div class="draft-review">
                    <p>Submitted: ${new Date(draft.submitted_at).toLocaleString()}</p>
                    <pre>${draft.content}</pre>
                </div>
            `).join('') || '<p>No drafts submitted yet.</p>');
    } catch (error) {
        handleError(error);
    }
}

// --- Task Approval Workflow ---
function loadTasksForApproval() {
    fetch('/get_tasks_for_approval')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('tasks-for-approval-container');
            if (!container) return;
            container.innerHTML = '';
            if (data.tasks && data.tasks.length > 0) {
                const table = document.createElement('table');
                table.className = 'tasks-table';
                table.innerHTML = `
                    <thead><tr><th>Task ID</th><th>Title</th><th>Requester</th><th>Created</th><th>Objective</th><th>Actions</th></tr></thead>
                    <tbody>
                        ${data.tasks.map(task => `
                            <tr>
                                <td>${task.task_id}</td>
                                <td>${task.task_title}</td>
                                <td>${task.requester_name || task.requester_id}</td>
                                <td>${new Date(task.created_at).toLocaleDateString()}</td>
                                <td>${task.objective_title || 'N/A'}</td>
                                <td><button class="view-task-btn" data-task-id="${task.task_id}">Details</button></td>
                            </tr>`).join('')}
                    </tbody>`;
                container.appendChild(table);
                container.querySelectorAll('.view-task-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        showTaskApprovalModal(this.dataset.taskId);
                    });
                });
            } else {
                container.innerHTML = '<p>No tasks pending approval.</p>';
            }
        })
        .catch(handleError);
}

function showTaskApprovalModal(taskId) {
    fetch(`/get_task?task_id=${taskId}`)
        .then(response => response.json())
        .then(task => {
            const detailsContainer = document.getElementById('task-approval-details');
            if (!detailsContainer) return;
            detailsContainer.innerHTML = `
                <h3>${task.task_title}</h3>
                <p><strong>Description:</strong> ${task.task_description}</p>
                <p><strong>Requester:</strong> ${task.requester_id}</p>
                <h4>KPAs:</h4>
                <ul>${task.progress.map(kpa => `<li>${kpa.description.text} (Due: ${new Date(kpa.description.date).toLocaleDateString()})</li>`).join('')}</ul>`;
            document.getElementById('approval-task-id').value = taskId;
            document.getElementById('task-approval-modal').style.display = 'block';
        })
        .catch(handleError);
}

function submitTaskApproval(taskId, decision, comments) {
    fetch('/approve_task', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ task_id: taskId, decision, comments })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) throw new Error(data.error);
        alert(data.message);
        document.getElementById('task-approval-modal').style.display = 'none';
        loadTasksForApproval();
    })
    .catch(handleError);
}

// #endregion

// =================================================================================
// #region VISUALIZATIONS (D3, etc.)
// =================================================================================

// --- Objective Management & D3 Tree ---
function appendNewObjective(objective, id) {
    const card = document.createElement('div');
    card.className = 'objective-card';
    card.dataset.id = id;
    card.innerHTML = `
        <div class="objective-title">${objective.title}</div>
        <div class="objective-description">${objective.description}</div>
        <button class="edit-btn" data-id="${id}"><i class="fas fa-edit"></i></button>
        <button class="delete-btn" data-id="${id}"><i class="fas fa-trash-alt"></i></button>
    `;
    document.getElementById('objectives-container')?.appendChild(card);
}

function deleteObjective(rowId) {
    fetch(`/delete_objective/${rowId}`, { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'deleted') {
                document.querySelector(`.objective-card[data-id="${rowId}"]`)?.remove();
            }
        })
        .catch(handleError);
}

function processObjectivesData(data) {
    const root = { name: "Objectives", children: [] };
    const map = new Map(data.map(obj => [obj.title, { ...obj, children: [] }]));
    for (const obj of map.values()) {
        if (obj.parent_title && map.has(obj.parent_title)) {
            map.get(obj.parent_title).children.push(obj);
        } else if (obj.type === 'organisational') {
            root.children.push(obj);
        }
    }
    return root;
}





// --- Radar Chart ---
function generateRadarChart(employeeData) {
    if (!window.RadarChart) return;
    const container = "#skillsRadarChart";
    d3.select(container).selectAll("*").remove();
    if (!employeeData?.skills?.length) return;

    const totalSkills = employeeData.skills.reduce((sum, skill) => sum + skill.count, 0);
    const radarData = [employeeData.skills.map(skill => ({
        axis: skill.skill,
        value: totalSkills > 0 ? skill.count / totalSkills : 0,
        category: skill.category,
        count: skill.count
    }))];

    const margin = { top: 100, right: 100, bottom: 100, left: 100 };
    const width = Math.min(700, window.innerWidth - 10) - margin.left - margin.right;
    const height = Math.min(width, window.innerHeight - margin.top - margin.bottom - 20);

    const config = {
        w: width, h: height, margin: margin, maxValue: 1, levels: 5, roundStrokes: true,
        color: d3.scaleOrdinal().range(["#EDC951", "#CC333F"]),
        labelFactor: 1.25, wrapWidth: 60, opacityArea: 0.35,
        dotRadius: 4, opacityCircles: 0.1, strokeWidth: 2
    };

    RadarChart(container, radarData, config);
}


// --- Calendar Heatmap ---
function initFlaskCalendarHeatmap(flaskData) {
    if (!window.calendarHeatmap) return;
    const calendarContainer = document.getElementById('calendar');
    if (!calendarContainer) return;
    calendarContainer.innerHTML = '';
    const heatmapData = generateHeatmapData(flaskData);
    if (!heatmapData.length) return;
    calendarHeatmap.init(heatmapData, calendarContainer);
}

function generateHeatmapData(flaskData) {
    const groupedData = (flaskData.heatmap_data || []).reduce((acc, entry) => {
        const dateKey = entry.date.split('T')[0];
        if (!acc[dateKey]) {
            acc[dateKey] = { date: new Date(dateKey), details: [], summary: [] };
        }
        acc[dateKey].details.push({ name: entry.task_title, value: entry.value, description: entry.description });
        acc[dateKey].summary = acc[dateKey].details;
        return acc;
    }, {});
    return Object.values(groupedData).map(entry => ({...entry, init: function() { this.total = this.details.reduce((a, d) => a + d.value, 0); return this; }}).init());
}

// #endregion

// =================================================================================
// #region HELPERS & UTILITIES
// =================================================================================

function calculateProgressWidth(progress) {
    if (!progress?.length) return 0;
    const approved = progress.filter(p => p.status === 'Approved').length;
    return Math.round((approved / progress.length) * 100);
}

function getProgressClass(status) {
    const classMap = {
        'Approved': 'completed',
        'Revisions Requested': 'revisions-needed',
        'Under Review': 'under-review',
        'Not Started': 'not-started'
    };
    return classMap[status] || 'in-progress';
}

function handleResponse(response) {
    return response.json().then(data => {
        if (!response.ok) {
            throw new Error(data.error || 'Network response was not ok');
        }
        if (data.ranking) {
            displayRecommendations(data.ranking);
        }
        return data;
    });
}

function handleError(error) {
    console.error('Error:', error);
    let errorContainer = document.getElementById('global-error-message');
    if (!errorContainer) {
        errorContainer = document.createElement('div');
        errorContainer.id = 'global-error-message';
        errorContainer.className = 'error-message';
        document.querySelector('main').prepend(errorContainer);
    }
    errorContainer.textContent = `An error occurred: ${error.message}`;
    errorContainer.style.display = 'block';
    setTimeout(() => { errorContainer.style.display = 'none'; }, 5000);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
// #endregion