/* Brutalist CSS */
body {
  font-family: 'Open Sans', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  margin: 0;
  padding: 0;
}

header {
  background-color: #000;
  color: #fff;
  padding: 1rem;
  text-align: center;
  font-size: 1.5rem;
}

#navigation {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

#navigation a {
  color: #fff;
  text-decoration: none;
  font-size: 1rem;
}

main {
  padding: 2rem;
}

#button-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 2rem;
}

button {
  background-color: #000;
  color: #fff;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #333;
}

.section-content {
  background-color: #fff;
  padding: 2rem;
  border: 2px solid #000;
  margin-bottom: 2rem;
}

form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

label {
  font-weight: bold;
}

input, select, textarea {
  padding: 0.5rem;
  border: 2px solid #000;
  font-size: 1rem;
}

hr {
  border: 2px solid #000;
  margin: 2rem 0;
}




/* New styles for the two-column layout */
#employee-skills-chart {
  display: flex;
  align-items: flex-start;
}

.radar-container {
  flex: 3; /* Larger column for the graph */
  margin-right: 20px; /* Space between the columns */
}

.radar-tooltip-container {
  flex: 1; /* Smaller column for the tooltip */
  /* background-color: #f5f5f5; */
  background-color: #fff;
  padding: 10px;
  /* border: 1px solid #ccc; */
  border-radius: 5px;
}

.radar-tooltip-content p {
  margin: 5px 0;
}

