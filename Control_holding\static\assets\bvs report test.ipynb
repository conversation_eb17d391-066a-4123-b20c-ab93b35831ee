{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Timestamp</th>\n", "      <th>a_location</th>\n", "      <th>a_number</th>\n", "      <th>b_number</th>\n", "      <th>Destination</th>\n", "      <th>CDRServiceType_Ref</th>\n", "      <th>BillingPrice_Ref</th>\n", "      <th>CDRContent_Ref</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-02-21 00:00:00</td>\n", "      <td>NaN</td>\n", "      <td>2.332566e+11</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BILLCYCLE_BEGIN</td>\n", "      <td>0.0</td>\n", "      <td>begin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-02-21 00:00:00</td>\n", "      <td>NaN</td>\n", "      <td>2.332570e+11</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BILLCYCLE_BEGIN</td>\n", "      <td>0.0</td>\n", "      <td>begin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-02-21 00:00:00</td>\n", "      <td>NaN</td>\n", "      <td>2.332570e+11</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BILLCYCLE_BEGIN</td>\n", "      <td>0.0</td>\n", "      <td>begin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-02-21 00:00:00</td>\n", "      <td>NaN</td>\n", "      <td>2.332570e+11</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BILLCYCLE_BEGIN</td>\n", "      <td>0.0</td>\n", "      <td>begin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-02-21 00:00:00</td>\n", "      <td>NaN</td>\n", "      <td>2.332570e+11</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>BILLCYCLE_BEGIN</td>\n", "      <td>0.0</td>\n", "      <td>begin</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Timestamp a_location      a_number b_number Destination  \\\n", "0  2024-02-21 00:00:00        NaN  2.332566e+11      NaN         NaN   \n", "1  2024-02-21 00:00:00        NaN  2.332570e+11      NaN         NaN   \n", "2  2024-02-21 00:00:00        NaN  2.332570e+11      NaN         NaN   \n", "3  2024-02-21 00:00:00        NaN  2.332570e+11      NaN         NaN   \n", "4  2024-02-21 00:00:00        NaN  2.332570e+11      NaN         NaN   \n", "\n", "  CDRServiceType_Ref  Billing<PERSON>rice_Ref CDRContent_Ref  \n", "0    BILLCYCLE_BEGIN               0.0          begin  \n", "1    BILLCYCLE_BEGIN               0.0          begin  \n", "2    BILLCYCLE_BEGIN               0.0          begin  \n", "3    BILLCYCLE_BEGIN               0.0          begin  \n", "4    BILLCYCLE_BEGIN               0.0          begin  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\CDR_CompareResults_-_2024-02-21_00_00_to_2024-02-22_12_01.csv\")\n", "\n", "df.head()\n", "\n", "# Timestamp\ta_location\ta_number\tb_number\tDestination\tCDRServiceType_Ref\tBillingPrice_Ref\tCDRContent_Ref\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['CompareType'] = df[df[\"CompareType\"].astype(str).]\n", "\n", "'string'."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 2}