

    function loadSectionContent(section) {
        // Hide all sections
        document.querySelectorAll('.section-content').forEach(sec => {
            sec.style.display = 'none';
        });

        // Show the requested section
        const sectionEl = document.getElementById(section);
        if (sectionEl) {
            sectionEl.style.display = 'block';
        } else {
            console.error('Section element not found:', section);
        }
    }
document.addEventListener('DOMContentLoaded', function() {

    loadSectionContent('user-profile');





    // Load objectives when the page loads
    fetch('/objectives')
        .then(response => response.json())
        .then(data => {
            console.log(data.objectives); // Log the received data to confirm its structure

            // Process and create the collapsible tree with the fetched data
            const processedData = processObjectivesData(data.objectives); // Ensure data is correctly formatted for processing
            // console.log('processed', processedData)
            createCollapsibleTree(processedData);

            data.objectives.forEach(row => {
                if(row.type ==='organisational'){
                    appendNewObjective(row, row.id)
                }
            }); // Adjust according to your data's structure


        })
        .catch(err => console.error('Error loading page content:', err));


    // Event listener for modal operations
    document.getElementById('add-objective-btn').addEventListener('click', () => {
        document.getElementById('objective-modal').style.display = 'block';
    });

    document.querySelector('.close').addEventListener('click', () => {
        document.getElementById('objective-modal').style.display = 'none';
    });

    window.onclick = function(event) {
        var modal = document.getElementById('objective-modal');
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    };

    // Button row event listeners
    document.querySelectorAll('#button-row button').forEach(button => {
        button.addEventListener('click', function() {
            const section = this.dataset.section;
            loadSectionContent(section);

            // If objectives tree is selected, refresh the tree
            if (section === 'objectives-tree') {
                // Refresh objectives data and rebuild the tree
                fetch('/objectives')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Refreshing objectives tree');
                        // Clear existing tree
                        document.getElementById('tree-container').innerHTML = '';
                        // Process and create the collapsible tree with the fetched data
                        const processedData = processObjectivesData(data.objectives);
                        createCollapsibleTree(processedData);
                    })
                    .catch(err => console.error('Error refreshing objectives tree:', err));
            }
        });
    });



    // Handle form submission for new objective
    document.getElementById('create-objective-form').addEventListener('submit', function(e) {
        e.preventDefault();
        var formData = new FormData(this);

//     // Get form data
    var cardData = {
        'title': this.querySelector('input[name="title"]').value,
        'description': this.querySelector('textarea[name="description"]').value,
        'type':'organisational'
    };

        fetch('/objectives/add', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('Objective added:', data);
            appendNewObjective(cardData, data.id); // Assuming your server responds with the id of the new objective
            document.getElementById('objective-modal').style.display = 'none';
            this.reset();
        })
        .catch(error => console.error('Error adding objective:', error));
    });

    // Event delegation for delete and edit buttons within objectives-container
    var objectivesContainer = document.getElementById('objectives-container');

    objectivesContainer.addEventListener('click', function(event) {
        if (event.target.closest('.delete-btn')) {
            var rowId = event.target.closest('.delete-btn').getAttribute('data-id');
            console.log(rowId)
            deleteObjective(rowId);
        } else if (event.target.closest('.edit-btn')) {
            var rowId = event.target.closest('.edit-btn').getAttribute('data-id');
            editObjective(rowId); // Function editObjective needs to be defined based on your requirements
        }
    });


function deleteObjective(rowId) {
    fetch(`/delete_objective/${rowId}`, { method: 'POST' })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'deleted') {
            const cardToDelete = document.querySelector(`.objective-card[data-id="${rowId}"]`);
            if (cardToDelete) {
                console.log('Objective deleted:', data);
                // Remove the card from the objectives-container
                cardToDelete.parentNode.removeChild(cardToDelete);
            } else {
                console.error('Card to delete not found:', rowId);
            }
        }
    })
    .catch(error => console.error('Error deleting objective:', error));
}



function appendNewObjective(objective, id) {
    var card = document.createElement('div');
    card.className = 'objective-card';
    card.setAttribute('data-id', id); // Setting data-id for the card
    card.innerHTML = `
        <div class="objective-title">${objective.title}</div>
        <div class="objective-description">${objective.description}</div>
        <button class="edit-btn" data-id="${id}"><i class="fas fa-edit"></i></button>
        <button class="delete-btn" data-id="${id}"><i class="fas fa-trash-alt"></i></button>
    `;
    document.getElementById('objectives-container').appendChild(card);
}


// Processes the objectives data into a hierarchical structure for D3
function processObjectivesData(data) {
    const root = {
        name: "Objectives",
        description: "All organizational objectives",
        children: []
    };

    // Filter out organizational objectives
    const orgObjectives = data.filter(d => d.type === 'organisational');
    orgObjectives.forEach(org => {
        const orgNode = {
            name: org.title,
            description: org.description || "",
            children: []
        };

        // Filter out divisional objectives that are children of the current organizational objective
        const divisionalObjectives = data.filter(d => d.type === 'divisional' && d.parent_title === org.title);
        divisionalObjectives.forEach(div => {
            const divNode = {
                name: div.title,
                description: div.description || "",
                children: []
            };

            // Filter out unit objectives that are children of the current divisional objective
            const unitObjectives = data.filter(d => d.type === 'unit' && d.parent_title === div.title);
            unitObjectives.forEach(unit => {
                const unitNode = {
                    name: unit.title,
                    description: unit.description || "",
                    children: [] // Assuming no further nesting
                };
                divNode.children.push(unitNode);
            });

            // Store children in _children and set children to null to collapse
            if (divNode.children && divNode.children.length > 0) {
                divNode._children = divNode.children;
                divNode.children = null;
            }

            orgNode.children.push(divNode);
        });

        // Keep organizational objectives expanded by default
        // We don't collapse them so they show their children

        root.children.push(orgNode);
    });

    return root;
}


// Placeholder for the function that creates the collapsible tree using D3
function createCollapsibleTree(data) {
    // Set the dimensions and margins of the diagram
    var margin = {top: 20, right: 90, bottom: 30, left: 90},
        width = 1060 - margin.right - margin.left,
        height = 950 - margin.top - margin.bottom;

    // append the svg object to the body of the page
    var svg = d3.select("#tree-container").append("svg")
        .attr("width", width + margin.right + margin.left)
        .attr("height", height + margin.top + margin.bottom)
      .append("g")
        .attr("transform", "translate(" + margin.left + "," + margin.top + ")");

    var i = 0,
        duration = 750,
        root;

    // Declares a tree layout and assigns the size
    var treemap = d3.tree().size([height, width]);

    // Assigns parent, children, height, depth
    root = d3.hierarchy(data, function(d) { return d.children; });
    root.x0 = height / 2;
    root.y0 = 0;

    // Collapse after the second level
    root.children.forEach(collapse);

    // Define the diagonal function for path generation

        // var diagonal = d3.linkHorizontal()
        //     .x(function(d) { return d.y; })
        //     .y(function(d) { return d.x; });

      function diagonal(s, d) {
        path = `M ${s.y} ${s.x}
                C ${(s.y + d.y) / 2} ${s.x},
                  ${(s.y + d.y) / 2} ${d.x},
                  ${d.y} ${d.x}`
        return path;
      }

    update(root);

    // Collapse the node and all its children
    function collapse(d) {
      if(d.children) {
        d._children = d.children
        d._children.forEach(collapse)
        d.children = null
      }
    }


    function update(source) {
        // Assigns the x and y position for the nodes
        var treeData = treemap(root);

        // Compute the new tree layout.
        var nodes = treeData.descendants(),
            links = treeData.descendants().slice(1);

        // Normalize for fixed-depth.
        nodes.forEach(function(d){ d.y = d.depth * 180; });

        // ****************** Nodes section ***************************

        // Update the nodes...
        var node = svg.selectAll('g.node')
            .data(nodes, function(d) { return d.id || (d.id = ++i); });

        // Enter any new nodes at the parent's previous position.
        var nodeEnter = node.enter().append('g')
            .attr('class', 'node')
            .attr("transform", function(_d) {
              return "translate(" + source.y0 + "," + source.x0 + ")"; })
            .on('click', function(d) {
                // Call the click function with the data
                click(d);
                // Prevent event bubbling
                d3.event.stopPropagation();
            });

        // Add Circle for the nodes
        nodeEnter.append('circle')
            .attr('class', 'node')
            .attr('r', 1e-6)
            .style("fill", function(d) {
                return d._children ? "lightsteelblue" : "#fff"; });

        // Add labels for the nodes
        nodeEnter.append('text')
            .attr("dy", ".35em")
            .attr("x", function(d) {
                return d.children || d._children ? -13 : 13; })
            .attr("text-anchor", function(d) {
                return d.children || d._children ? "end" : "start"; })
            .text(function(d) {
                // Combine title and description, but keep it reasonably short
                const title = d.data.name || '';
                const description = d.data.description || '';
                if (description && description.length > 0) {
                    // Limit the combined text to a reasonable length
                    const combinedText = title + ': ' + description;
                    return combinedText.length > 50 ? combinedText.substring(0, 47) + '...' : combinedText;
                } else {
                    return title;
                }
            })
            .append("title") // Add tooltip with full text
            .text(function(d) {
                const title = d.data.name || '';
                const description = d.data.description || '';
                return description ? title + ': ' + description : title;
            });

        // UPDATE
        var nodeUpdate = nodeEnter.merge(node);

        // Transition to the proper position for the node
        nodeUpdate.transition()
          .duration(duration)
          .attr("transform", function(d) {
              return "translate(" + d.y + "," + d.x + ")"; });

        // Update the node attributes and style
        nodeUpdate.select('circle.node')
          .attr('r', 10)
          .style("fill", function(d) {
              return d._children ? "lightsteelblue" : "#fff"; })
          .attr('cursor', 'pointer');

        // Remove any exiting nodes
        var nodeExit = node.exit().transition()
            .duration(duration)
            .attr("transform", function(_d) {
                return "translate(" + source.y + "," + source.x + ")"; })
            .remove();

        // On exit reduce the node circles size to 0
        nodeExit.select('circle')
          .attr('r', 1e-6);

        // On exit reduce the opacity of text labels
        nodeExit.select('text')
          .style('fill-opacity', 1e-6);

        // ****************** links section ***************************

        // Update the links...
        var link = svg.selectAll('path.link')
            .data(links, function(d) { return d.id; });

        // Enter any new links at the parent's previous position.
        var linkEnter = link.enter().insert('path', "g")
            .attr("class", "link")
            .attr('d', function(_d){
              var o = {x: source.x0, y: source.y0}
              return diagonal(o, o)
            });

        // UPDATE
        var linkUpdate = linkEnter.merge(link);

        // Transition back to the parent element position
        linkUpdate.transition()
            .duration(duration)
            .attr('d', function(d){ return diagonal(d, d.parent) });

        // Remove any exiting links
        link.exit().transition()
            .duration(duration)
            .attr('d', function(_d) {
              var o = {x: source.x, y: source.y}
              return diagonal(o, o)
            })
            .remove();

        // Store the old positions for transition.
        nodes.forEach(function(d){
          d.x0 = d.x;
          d.y0 = d.y;
        });
      }
    // Toggle children on click.
    function click(event, d) {
        // D3 v6+ passes the event as first parameter and the data as second
        // For D3 v4 (which is being used here), we need to handle it differently
        if (!d && event) {
            // If d is not defined but event is, then event is actually the data
            d = event;
        }

        console.log("Node clicked:", d);

        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
        update(d);

        // Prevent event bubbling
        if (d3.event) {
            d3.event.stopPropagation();
        }
    }
  }







function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Example usage with a search function
const debouncedSearch = debounce(function searchTerm(searchTerm) {
    console.log('Searching for:', searchTerm);
    // Implement your fetch request here
    fetch(`/search_employees?search=${encodeURIComponent(searchTerm)}`)
    .then(response => response.json())
    .then(data => showSearch(data))
    .catch(error => console.error('Error:', error));
}, 250); // Waits for 250ms of no new keystrokes

document.getElementById('employee-search-input').addEventListener('input', (e) => {
// Immediately hide previous results and clear the container
    $('#employee-search-results').empty().hide();
    const searchTerm = e.target.value.trim();
    debouncedSearch(searchTerm);
});

    function showSearch(data) {
        if (data && data.length > 0) {
            // Append new suggestions to the search results container
            data.forEach(employee => {
                $('#employee-search-results').append(`<div class="search-result-item" data-id="${employee.id}">${employee.name}</div>`);
            });
            // Show the search results container only if there are results
            $('#employee-search-results').show();
        }
    }

    // Attach click event to the parent container, and use event delegation
$('#employee-search-results').on('click', '.search-result-item', function() {
    var employeeId = $(this).attr('data-id');
    console.log('Employee ID clicked:', employeeId);
    // Now you can fetch more details about the employee or navigate to a detail view
    loadEmployeeData(employeeId); // Example function call
    $('#employee-search-input').val($(this).text().trim()); // Optional: Fill input with selected name
    $('#employee-search-results').empty().hide(); // Clear and hide suggestions
});



// Function to load employee data and pass it to the appropriate visualization functions
function loadEmployeeData(employeeId) {
    fetch(`/get_employee_data/${employeeId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                console.error('Error loading employee data:', data.error);
                return;
            }
            console.log('Employee Data from Flask:', data);

            // Pass the data to heatmap and radar chart initialization functions
            initFlaskCalendarHeatmap(data);

            generateRadarChart(data);


        })
        .catch(err => console.error('Error loading employee data:', err));
}


// Function to initialize the calendar heatmap
// function initFlaskCalendarHeatmap(flaskData) {
//     const heatmapData = generateHeatmapData(flaskData);

//     if (!heatmapData.length) {
//         console.warn("No progress data found for heatmap.");
//         return;
//     }

//     const div_id = 'calendar';
//     const color = '#cd2327'; // Heatmap color
//     const overview = 'year'; // Initial overview type

//     const print = val => console.log("Heatmap event:", val);

//     // Initialize the calendar heatmap
//     calendarHeatmap.init(heatmapData, div_id, color, overview, print);
// }

// // Function to generate heatmap data from Flask data
// function generateHeatmapData(flaskData) {
//     if (!flaskData.heatmap_data || !Array.isArray(flaskData.heatmap_data)) {
//         return [];
//     }

//     const dayMap = {};

//     flaskData.heatmap_data.forEach(entry => {
//         const dayKey = entry.date;
//         if (!dayMap[dayKey]) {
//             dayMap[dayKey] = [];
//         }

//         dayMap[dayKey].push({
//             name: entry.task_title || 'Unnamed Task',
//             date: new Date(entry.date),
//             value: entry.value || 1,
//         });
//     });

//     return Object.keys(dayMap).map(dayKey => {
//         const dayDate = new Date(dayKey);
//         const details = dayMap[dayKey];

//         return {
//             date: dayDate,
//             details: details,
//             summary: details,
//             init: function () {
//                 this.total = this.details.reduce((acc, d) => acc + d.value, 0);
//                 return this;
//             },
//         }.init();
//     });
// }

function generateRadarChart(employeeData) {
    // Clear previous chart
    d3.select("#skillsRadarChart").selectAll("*").remove();

    console.log('Employee Data:', employeeData);

    if (!employeeData || !employeeData.skills) {
        console.error("No skills data available");
        return;
    }


    // Calculate total number of skills
    const totalSkills = employeeData.skills.reduce((sum, skill) => sum + skill.count, 0);

    // Create data array suitable for Radar Chart
    const radarData = employeeData.skills.map(skill => ({
        axis: skill.skill,
        value: totalSkills > 0 ? skill.count / totalSkills : 0,
        category: skill.category,
        count: skill.count
    }));

    console.log('Radar Data:', radarData);
    const data = [radarData];

    // Chart configuration
    const margin = { top: 100, right: 100, bottom: 100, left: 100 };
    const width = Math.min(700, window.innerWidth - 10) - margin.left - margin.right;
    const height = Math.min(width, window.innerHeight - margin.top - margin.bottom - 20);

    const config = {
        w: width,
        h: height,
        margin: margin,
        maxValue: 1, // Percentage is between 0 and 1
        levels: 5,
        roundStrokes: true,
        color: d3.scaleOrdinal()
            .range(["#EDC951", "#CC333F"]), // First color for soft skills, second for hard skills
        labelFactor: 1.25,
        wrapWidth: 60,
        opacityArea: 0.35,
        dotRadius: 4,
        opacityCircles: 0.1,
        strokeWidth: 2
    };

    // Draw the chart
    RadarChart("#skillsRadarChart", data, config);
}



function showModal(skillData) {
    const tooltipContent = document.getElementById("tooltip-content");
    const skillAxis = document.getElementById("skill-axis");
    const skillCategory = document.getElementById("skill-category");
    const skillCount = document.getElementById("skill-count");
    const skillPercentage = document.getElementById("skill-percentage");

    if (!tooltipContent || !skillAxis || !skillCategory || !skillCount || !skillPercentage) {
        console.error("One or more tooltip elements are missing.");
        return;
    }

    // Populate the tooltip with the skill data
    skillAxis.textContent = `Skill: ${skillData.axis}`;
    skillCategory.textContent = `Category: ${skillData.category.charAt(0).toUpperCase() + skillData.category.slice(1)}`;
    skillCount.textContent = `Count: ${skillData.count}`;
    skillPercentage.textContent = `Percentage: ${(skillData.value * 100).toFixed(1)}%`;

    // Show the tooltip content
    tooltipContent.style.display = "block";
}

function hideTooltip() {
    const tooltipContent = document.getElementById("tooltip-content");
    if (tooltipContent) {
        tooltipContent.style.display = "none";
    }
}



function RadarChart(id, data, options) {
        // Your existing configuration and setup code
        const cfg = {
            w: 600,
            h: 600,
            margin: { top: 20, right: 20, bottom: 20, left: 20 },
            levels: 3,
            maxValue: 0,
            labelFactor: 1.25,
            wrapWidth: 60,
            opacityArea: 0.35,
            dotRadius: 4,
            opacityCircles: 0.1,
            strokeWidth: 2,
            roundStrokes: false,
            color: d3.scaleOrdinal(d3.schemeCategory10),
            ...options,
        };
        // If the supplied maxValue is smaller than the actual one, replace by the max in the data
        cfg.maxValue = Math.max(cfg.maxValue, d3.max(data, d => d3.max(d.map(o => o.value))));
        const allAxis = Array.from(new Set(d3.merge(data).map((d) => d.axis)));
        const total = allAxis.length;
        const radius = Math.min(cfg.w / 2, cfg.h / 2);
        const angleSlice = (Math.PI * 2) / total;

        // Scale for the radius
        const rScale = d3
            .scaleLinear()
            .range([0, radius])
            .domain([0, cfg.maxValue]);

        // Remove whatever chart with the same id/class was present before
        d3.select(id).select("svg").remove();

        // Initiate the radar chart SVG
        const svg = d3
            .select(id)
            .append("svg")
            .attr("width", cfg.w + cfg.margin.left + cfg.margin.right)
            .attr("height", cfg.h + cfg.margin.top + cfg.margin.bottom)
            .attr("class", "radar");

        // Append a g element
        const g = svg
            .append("g")
            .attr(
                "transform",
                `translate(${cfg.w / 2 + cfg.margin.left}, ${cfg.h / 2 + cfg.margin.top})`
            );

        // Select the tooltip div
        const tooltip = d3.select(".radar-tooltip");

        // Circular grid
        const axisGrid = g.append("g").attr("class", "axisWrapper");

        // Draw the background circles
        axisGrid
            .selectAll(".levels")
            .data(d3.range(1, cfg.levels + 1).reverse())
            .enter()
            .append("circle")
            .attr("class", "gridCircle")
            .attr("r", (d) => (radius * d) / cfg.levels)
            .style("fill", "#CDCDCD")
            .style("stroke", "#CDCDCD")
            .style("fill-opacity", cfg.opacityCircles);

        // Text indicating at what % each level is
        axisGrid
            .selectAll(".axisLabel")
            .data(d3.range(1, cfg.levels + 1).reverse())
            .enter()
            .append("text")
            .attr("class", "axisLabel")
            .attr("x", 4)
            .attr("y", (d) => (-d * radius) / cfg.levels)
            .attr("dy", "0.4em")
            .style("font-size", "10px")
            .attr("fill", "#737373")
            .text((d) => Math.round((cfg.maxValue * d) / cfg.levels * 100) + "%");

        // Create the straight lines radiating outward from the center
        const axis = axisGrid
            .selectAll(".axis")
            .data(allAxis)
            .enter()
            .append("g")
            .attr("class", "axis");

        // Append the lines
        axis
            .append("line")
            .attr("x1", 0)
            .attr("y1", 0)
            .attr("x2", (_d, i) =>
                rScale(cfg.maxValue * 1.1) * Math.cos(angleSlice * i - Math.PI / 2)
            )
            .attr("y2", (_d, i) =>
                rScale(cfg.maxValue * 1.1) * Math.sin(angleSlice * i - Math.PI / 2)
            )
            .attr("class", "line")
            .style("stroke", "white")
            .style("stroke-width", "2px");

        // Append the labels at each axis
        axis
            .append("text")
            .attr("class", "legend")
            .style("font-size", "11px")
            .attr("text-anchor", "middle")
            .attr("dy", "0.35em")
            .attr("x", (_d, i) =>
                rScale(cfg.maxValue * cfg.labelFactor) *
                Math.cos(angleSlice * i - Math.PI / 2)
            )
            .attr("y", (_d, i) =>
                rScale(cfg.maxValue * cfg.labelFactor) *
                Math.sin(angleSlice * i - Math.PI / 2)
            )
            .text((d) => d)
            .call(wrap, cfg.wrapWidth);

        // The radial line function
        const radarLine = d3
            .lineRadial()
            .curve(cfg.roundStrokes ? d3.curveCardinalClosed : d3.curveLinearClosed)
            .radius((d) => rScale(d.value))
            .angle((_d, i) => i * angleSlice);

        // Create a wrapper for the blobs
        const blobWrapper = g
            .selectAll(".radarWrapper")
            .data(data)
            .enter()
            .append("g")
            .attr("class", "radarWrapper");

        // Append the backgrounds
        blobWrapper
            .append("path")
            .attr("class", "radarArea")
            .attr("d", (d) => radarLine(d))
            .style("fill", (_d, i) => cfg.color(i))
            .style("fill-opacity", cfg.opacityArea)
            .on("mouseover", function () {
                // Dim all blobs
                d3.selectAll(".radarArea")
                    .transition()
                    .duration(200)
                    .style("fill-opacity", 0.1);
                // Highlight the hovered blob
                d3.select(this)
                    .transition()
                    .duration(200)
                    .style("fill-opacity", 0.7);
            })
            .on("mouseout", function () {
                // Restore opacity
                d3.selectAll(".radarArea")
                    .transition()
                    .duration(200)
                    .style("fill-opacity", cfg.opacityArea);
            });

        // Create the outlines
        blobWrapper
            .append("path")
            .attr("class", "radarStroke")
            .attr("d", (d) => radarLine(d))
            .style("stroke-width", cfg.strokeWidth + "px")
            .style("stroke", (d, _i) => (d[0].category === "soft" ? "#EDC951" : "#CC333F"))
            .style("fill", "none");

        // Append the circles
        blobWrapper
            .selectAll(".radarCircle")
            .data((d) => d)
            .enter()
            .append("circle")
            .attr("class", "radarCircle")
            .attr("r", cfg.dotRadius)
            .attr("cx", (d, i) =>
                rScale(d.value) * Math.cos(angleSlice * i - Math.PI / 2)
            )
            .attr("cy", (d, i) =>
                rScale(d.value) * Math.sin(angleSlice * i - Math.PI / 2)
            )
            .style("fill", (d) => (d.category === "soft" ? "#EDC951" : "#CC333F"))
            .style("fill-opacity", 0.8);

        // Wrapper for the invisible circles on top
        const blobCircleWrapper = g
            .selectAll(".radarCircleWrapper")
            .data(data)
            .enter()
            .append("g")
            .attr("class", "radarCircleWrapper");

        // Append a set of invisible circles on top for the mouseover pop-up
        blobCircleWrapper
            .selectAll(".radarInvisibleCircle")
            .data((d) => d)
            .enter()
            .append("circle")
            .attr("class", "radarInvisibleCircle")
            .attr("r", cfg.dotRadius * 1.5)
            .attr("cx", (d, i) =>
                rScale(d.value) * Math.cos(angleSlice * i - Math.PI / 2)
            )
            .attr("cy", (d, i) =>
                rScale(d.value) * Math.sin(angleSlice * i - Math.PI / 2)
            )
            .style("fill", "none")
            .style("pointer-events", "all")
            .on("mouseover", function (event, d) {
                // Access the parent data
                const parentData = d3.select(this.parentNode).datum();

                console.log("Parent Data:", parentData);
                console.log("Current d:", d);

                // Assuming 'd' is the index based on your fix
                const skillData = parentData[d];

                console.log("Found Skill:", skillData);

                if (!skillData) {
                    console.error(`Skill not found for index: ${d}`);
                    return; // Exit if skill is not found to prevent further errors
                }

                // Calculate the percentage
                const percentage = (skillData.value * 100).toFixed(1) + "%";

                // Format category display
                const categoryDisplay = skillData.category
                    ? skillData.category.charAt(0).toUpperCase() + skillData.category.slice(1)
                    : "Unknown";

                // Tooltip content
                const tooltipText = `Skill: ${skillData.axis}<br>Category: ${categoryDisplay}<br>Count: ${skillData.count}<br>Percentage: ${percentage}`;
                showModal(skillData)

                // Show and position the tooltip
                // tooltip
                //     .html(tooltipText)
                //     .style("left", `${legendOffsetX}px`)
                //     .style("top", `${legendOffsetY}px`)
                //     .style("opacity", 1);
            })
            .on("mousemove", function (event, d) {
                // Update tooltip position as the mouse moves
                tooltip
                    .style("left", (event.pageX + 10) + "px")
                    .style("top", (event.pageY - 28) + "px");
            })
            .on("mouseout", function () {
                // Hide the tooltip when the mouse moves out
                hideTooltip();
            });

        // Create a legend
        const legendZone = svg.append("g");
        const names = ["Soft Skills", "Hard Skills"];
        const legend = legendZone
            .selectAll("g")
            .data(names)
            .enter()
            .append("g")
            .attr(
                "transform",
                (_d, i) => `translate(${cfg.w + cfg.margin.left + 20},${i * 20 + cfg.margin.top})`
            );

        legend
            .append("rect")
            .attr("width", 10)
            .attr("height", 10)
            .style("fill", (_d, i) => (i === 0 ? "#EDC951" : "#CC333F"));

        legend
            .append("text")
            .attr("x", 15)
            .attr("y", 10)
            .text((d) => d)
            .style("font-size", "11px")
            .attr("fill", "#737373");

        // Helper function to wrap text
        function wrap(text, width) {
            text.each(function () {
                var text = d3.select(this),
                    words = text.text().split(/\s+/).reverse(),
                    word,
                    line = [],
                    lineNumber = 0,
                    lineHeight = 1.4, // ems
                    y = text.attr("y"),
                    x = text.attr("x"),
                    dy = parseFloat(text.attr("dy")),
                    tspan = text
                        .text(null)
                        .append("tspan")
                        .attr("x", x)
                        .attr("y", y)
                        .attr("dy", dy + "em");

                while ((word = words.pop())) {
                    line.push(word);
                    tspan.text(line.join(" "));
                    if (tspan.node().getComputedTextLength() > width) {
                        line.pop();
                        tspan.text(line.join(" "));
                        line = [word];
                        tspan = text
                            .append("tspan")
                            .attr("x", x)
                            .attr("y", y)
                            .attr("dy", ++lineNumber * lineHeight + dy + "em")
                            .text(word);
                    }
                }
            });
        }
    }

});

// Task Assignment tab functions

    // document.addEventListener('DOMContentLoaded', function() {
    //     const taskTabs = document.querySelectorAll('#task-assignment-tabs button');
    //     const taskContents = document.querySelectorAll('.task-content');

    //     taskTabs.forEach(tab => {
    //         tab.addEventListener('click', function() {
    //             const tabId = this.getAttribute('data-tab');
    //             taskContents.forEach(content => {
    //                 content.classList.remove('active');
    //             });
    //             document.getElementById(tabId).classList.add('active');
    //         });
    //     });

    //     // KPA functionality for Assign Tasks
    //     const addKpaBtnAssign = document.getElementById('add-kpa-btn-assign');
    //     const kpaContainerAssign = document.getElementById('kpa-container-assign');

    //     addKpaBtnAssign.addEventListener('click', function() {
    //         addKpaInputGroup(kpaContainerAssign);
    //     });

    //     // KPA functionality for Self-Generated Tasks
    //     const addKpaBtnSelf = document.getElementById('add-kpa-btn-self');
    //     const kpaContainerSelf = document.getElementById('kpa-container-self');

    //     addKpaBtnSelf.addEventListener('click', function() {
    //         addKpaInputGroup(kpaContainerSelf);
    //     });

    //     // Add event listener to existing delete buttons
    //     const deleteKpaBtns = document.querySelectorAll('.delete-kpa');
    //     deleteKpaBtns.forEach(btn => {
    //         btn.addEventListener('click', function() {
    //             const kpaInputGroup = this.parentElement;
    //             kpaInputGroup.parentElement.removeChild(kpaInputGroup);
    //         });
    //     });

    //     function addKpaInputGroup(container) {
    //         const kpaInputGroup = document.createElement('div');
    //         kpaInputGroup.className = 'kpa-input-group';
    //         kpaInputGroup.innerHTML = `
    //             <input type="text" name="kpa[]" placeholder="Enter KPA" required>
    //             <button type="button" class="delete-kpa">Delete</button>
    //         `;
    //         container.appendChild(kpaInputGroup);

    //         // Add event listener to the delete button
    //         const deleteKpaBtn = kpaInputGroup.querySelector('.delete-kpa');
    //         deleteKpaBtn.addEventListener('click', function() {
    //             container.removeChild(kpaInputGroup);
    //         });
    //     }

    //     // Form submission for Assign Tasks
    //     const assignTaskForm = document.getElementById('assign-task-form');
    //     assignTaskForm.addEventListener('submit', function(event) {
    //         event.preventDefault();
    //         submitTaskForm('assign-task-form', 'assigned');
    //     });

    //     // Form submission for Self-Generated Tasks
    //     const selfGeneratedTaskForm = document.getElementById('self-generated-task-form');
    //     selfGeneratedTaskForm.addEventListener('submit', function(event) {
    //         event.preventDefault();
    //         submitTaskForm('self-generated-task-form', 'self-generated');
    //     });

    //     function submitTaskForm(formId, requestType) {
    //         const form = document.getElementById(formId);
    //         const formData = new FormData(form);
    //         const data = {};

    //         formData.forEach((value, key) => {
    //             if (key.includes('kpa')) {
    //                 if (!data.kpas) {
    //                     data.kpas = [];
    //                 }
    //                 data.kpas.push(value);
    //             } else {
    //                 data[key] = value;
    //             }
    //         });

    //         data.request_type = requestType;

    //         fetch('/assign_task', {
    //             method: 'POST',
    //             headers: {
    //                 'Content-Type': 'application/json'
    //             },
    //             body: JSON.stringify(data)
    //         })
    //         .then(response => response.json())
    //         .then(result => {
    //             if (result.ranking) {
    //                 displayRecommendations(result.ranking);
    //             } else if (result.error) {
    //                 alert('Error: ' + result.error);
    //             }
    //         })
    //         .catch(error => {
    //             console.error('Error:', error);
    //         });
    //     }



    // });

