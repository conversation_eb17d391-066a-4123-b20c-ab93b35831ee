<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard Page</title>
  <script src="https://code.jquery.com/jquery-1.10.2.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js" integrity="sha384-+sLIOodYLS7CIrQpBjl+C7nPvqq+FbNUBDunl/OZv93DB7Ln/533i8e/mZXLi/P+" crossorigin="anonymous"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">
  <link rel="stylesheet" href="/static/assets/styles.css">
	  <!-- Font Awesome Icons -->
  <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
  <!-- <style>
    body {
        width: 800px;
        margin: 0 auto;
    }

    .navigation {
        position: relative;
        display: inline-flex;
        background: lightgray;
        padding: 5px;
        border-radius: 50px;
    }

    .navigation__active-state {
        position: absolute;
        background: linear-gradient(160deg, rgb(51, 241, 172), rgb(16, 11, 62));
        height: calc(100% - 8px);
        width: 50px;
        border-radius: 50px;
        top: 4px;
        left: 4px;
        opacity: 0;
        transition: left 0.4s ease-out, width 0.4s ease-out, opacity 0.4s ease-out 0.4s;
    }

    .navigation__btn {
        cursor: pointer;
        color: darkred;
        padding: 8px 15px;
        transition: color 0.3s ease-out;
        z-index: 1;
        margin-right: 5px;
    }

    .navigation__btn:last-child {
        margin-right: 0;
    }

    .navigation__btn:hover {
        color: gray;
    }

    .navigation__btn--active {
        pointer-events: none;
    }
</style> -->
<style>
  
  .tab-slider-container {
      margin-top: 20px;
      text-align: center; /* Center the tab buttons */
    }

    .tab-slider {
      display: flex;
      flex-wrap: wrap; /* Allow buttons to wrap on small screens */
      justify-content: center; /* Center the buttons horizontally */
      border-bottom: 2px solid lightgray;
      /* border-radius: 50px; */
    }

    .tab-slider-button {
      cursor: pointer;
      padding: 10px 15px;
      transition: background-color 0.3s ease-out;
      font-weight: bold;
      margin: 5px; /* Add some spacing between buttons */
      border-radius: 50px;
    }

    .tab-slider-button.active {
      /* background-color: lightgray; */
      background: linear-gradient(160deg, rgb(51, 241, 172), rgb(76, 67, 155));
    }

    .tab-slider-content {
      display: none;
      padding: 20px;
      border: 2px solid lightgray;
    }

    .tab-slider-content.active {
      display: block;
    }

    /* Responsive styles for the aside with modal buttons */
        .modal-buttons {
      margin-top: 20px;
      text-align: center; /* Center buttons horizontally */
    }

/* Responsive styles for the aside with modal buttons */
.modal-buttons {
      margin-top: 20px;
      text-align: center; /* Center buttons horizontally */
      overflow: hidden; /* Hide content that overflows */
    }

    .dummy-button {
      display: block;
      margin: 5px auto; /* Center buttons horizontally and add spacing */
      padding: 10px 20px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    @media screen and (max-width: 768px) {
      .sidebar {
        left: 0;
      }

      .main-content {
        margin-left: 0;
      }
    }
</style>
</head>

<body>
    <div class="bg"></div>
    <div class="bg bg2"></div>
    <div class="bg bg3"></div>
    <button class="sidebar-toggle-button" onclick="toggleSidebar()">Toggle Sidebar
        <i class="fas fa-bars"></i>
      </button>
  <div class="container">
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-container">
          <div class="sidebar-header">
            <h2>Unify</h2>
          </div>
          <ul class="sidebar-menu">
            <li class="active"><a href="#">Human Resources</a></li>
            <li><a href="#">Tasks and Objectives</a></li>
            <li><a href="#">Reviews</a></li>
          </ul>
        </div>
      </aside>
      

    <main class="main-content" id="main-content">
      <header class="content-header">

<!-- insert tabbed slider here -->
<div class="tab-slider-container">
  <div class="tab-slider">
    <div class="tab-slider-button active">Employee Performance</div>
    <div class="tab-slider-button">Training and Development</div>
    <div class="tab-slider-button">Diversity and Inclusion</div>
    <div class="tab-slider-button">Employee Satisfaction</div>
    <div class="tab-slider-button">Workforce Demographics</div>
    <div class="tab-slider-button">Performance Reviews</div>
  </div>
</div>
      </header>
      <section class="content">
        <!-- Content for different sections will be loaded here -->
        <div class="tab-slider-content active">
          <!-- Employee Performance content -->
          <!-- ... -->
        </div>
        <div class="tab-slider-content">
          <!-- Training and Development content -->
          <!-- ... -->
        </div>
        <div class="tab-slider-content">
          <!-- Diversity and Inclusion content -->
          <!-- ... -->
        </div>
        <div class="tab-slider-content">
          <!-- Employee Satisfaction content -->
          <!-- ... -->
        </div>
        <div class="tab-slider-content">
          <!-- Workforce Demographics content -->
          <!-- ... -->
        </div>
        <div class="tab-slider-content">
          <!-- Performance Reviews content -->
          <!-- ... -->
        </div>
      </section>
    </main>

    <aside class="modal-buttons" style="margin-right: -15px;">
      <h3>Actions</h3>
      <button class="dummy-button" data-toggle="modal" data-target="#new-staff-modal">Create New Staff</button>
      <button class="dummy-button" data-modal="modal2">Modal 2</button>
      <button class="dummy-button" data-modal="modal3">Modal 3</button>
    </aside>
  </div>

  <!-- Modals will be implemented separately -->
  <div class="modal" id="new-staff-modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <form>
        <div class="modal-section">
          <h2>Basic Information</h2>
          <label for="first-name">First Name:</label>
          <input type="text" id="first-name" name="first-name" required>
          <label for="last-name">Last Name:</label>
          <input type="text" id="last-name" name="last-name" required>
          <label for="gender">Gender:</label>
          <select id="gender" name="gender" required>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
          </select>
          <label for="division">Gender:</label>
          <select id="division" name="division" required>
            <option value="Regulatory Administration">Regulatory Administration</option>
            <option value="Engineering">Engineering</option>
            <option value="Human Resources">Human Resources</option>
          </select>
          <label for="date-of-birth">Date of Birth:</label>
          <input type="date" id="date-of-birth" name="date-of-birth" required>
        </div>
        <div class="modal-section">
          <h2>Professional Background</h2>
          <label for="qualifications">Qualifications/Education:</label>
          <textarea id="qualifications" name="qualifications" rows="4" required></textarea>
          <label for="experience">Experience:</label>
          <textarea id="experience" name="experience" rows="4" required></textarea>
        </div>
        <button type="submit">Create</button>
      </form>
    </div>
  </div>
  <script>
    function toggleSidebar() {
      const sidebar = document.querySelector('.sidebar');
      const mainContent = document.querySelector('.main-content');
      sidebar.classList.toggle('hidden');
      mainContent.classList.toggle('expanded');
    }
  </script>
<!-- Add the JavaScript code -->
<script>
    $(document).ready(function() {
      // Show modal when the button is clicked
      $('.close').click(function() {
        $('#new-staff-modal').modal('hide');
      });
    });
  </script>

<script>
$(document).ready(function() {
    // AJAX function to send form data to the server
    $('#new-staff-form').submit(function(event) {
      event.preventDefault();
  
      // Get form data
      var formData = {
        firstName: $('#first-name').val(),
        lastName: $('#last-name').val(),
        gender: $('#gender').val(),
        dob: $('#dob').val(),
        division:$('#division').val(),
        qualifications: $('#qualifications').val(),
        experience: $('#experience').val()
      };
  
      // Send AJAX request to the server
      $.ajax({
        url: '/create_staff',
        type: 'POST',
        data: JSON.stringify(formData),
        contentType: 'application/json',
        success: function(response) {
          // Handle success response from the server
          console.log(response);
          // Optionally, perform any further actions or display a success message
        },
        error: function(error) {
          // Handle error response from the server
          console.error(error);
          // Optionally, display an error message to the user
        }
      });
    });
  });
</script>

<script>
  $(document).ready(function() {
    // Handle tab slider behavior
    $('.tab-slider-button').click(function() {
      $('.tab-slider-button').removeClass('active');
      $(this).addClass('active');
      $('.tab-slider-content').removeClass('active');
      $('.tab-slider-content').eq($(this).index()).addClass('active');
    });
  });
</script>

</body>

</html>
