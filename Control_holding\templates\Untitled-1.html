<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cal-Heatmap Example</title>
  <!-- jQuery CDN -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- D3.js CDN for Cal-Heatmap -->
  <script src="https://d3js.org/d3.v7.min.js"></script>
  <!-- Cal-Heatmap JS and CSS -->
  <script src="https://unpkg.com/cal-heatmap/dist/cal-heatmap.min.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/cal-heatmap/dist/cal-heatmap.css">
</head>
<body>
<div id="cal"></div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const cal = new CalHeatmap();
    
    const data = {
      // Convert your date strings to timestamps (seconds since epoch)
      '1325376000': 3,  // Date: '2012-01-01'
      '1325462400': 6,  // Date: '2012-01-02'
    };

    cal.paint({
      itemSelector: '#cal',
      domain: 'month',
      subDomain: 'day',
      data: data,
      start: new Date(2012, 0),  // January 2012
    });
  });
</script>
</body>
</html>
