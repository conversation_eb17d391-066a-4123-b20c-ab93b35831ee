document.addEventListener('DOMContentLoaded', function() {
    // Get the objectives button
    const objectivesButton = document.getElementById('objectives-button');

    // Add click event listener to the objectives button
    objectivesButton.addEventListener('click', function() {
        // Fetch objectives data from the server
        fetch('/objectives')
            .then(response => response.json())
            .then(data => {
                // Clear existing content
                const mainContent = document.querySelector('.main-content');
                mainContent.innerHTML = '';

                // Populate the main content with glassmorphic cards for each objective
                data['objectives'].forEach(objective => {
                    const card = createObjectiveCard(objective);
                    mainContent.appendChild(card);
                });
            })
            .catch(error => console.log(error));
    });

    // Function to create a glassmorphic card for an objective
    function createObjectiveCard(objective) {
        const card = document.createElement('div');
        card.className = 'card';

        const title = document.createElement('h2');
        title.textContent = objective.title;

        const description = document.createElement('p');
        description.textContent = objective.description;

        const deleteButton = document.createElement('button');
        deleteButton.className = 'delete-button';
        deleteButton.textContent = 'Delete';

        // Add click event listener to the delete button
        deleteButton.addEventListener('click', function() {
            // Implement delete functionality here
            // You can make an AJAX request to delete the objective and remove the card from the UI
        });

        card.appendChild(title);
        card.appendChild(description);
        card.appendChild(deleteButton);

        return card;
    }

    // Initialize Lottie animation
    lottie.loadAnimation({
        container: document.getElementById('lottie-animation'),
        renderer: 'svg',
        loop: false,
        autoplay: true,
        path: "https://assets6.lottiefiles.com/packages/lf20_Gpt6Y2.json" // Replace with the path to your Lottie animation JSON file
    });
});


