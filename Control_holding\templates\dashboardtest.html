<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            width: 800px;
            margin: 0 auto;
        }

        .navigation {
            position: relative;
            display: inline-flex;
            background: lightgray;
            padding: 5px;
            border-radius: 50px;
        }

        .navigation__active-state {
            position: absolute;
			background: linear-gradient(160deg, rgb(51, 241, 172), rgb(16, 11, 62));
            height: calc(100% - 8px);
            width: 50px;
            border-radius: 50px;
            top: 4px;
            left: 4px;
            opacity: 0;
            transition: left 0.4s ease-out, width 0.4s ease-out, opacity 0.4s ease-out 0.4s;
        }

        .navigation__btn {
            cursor: pointer;
            color: darkred;
            padding: 8px 15px;
            transition: color 0.3s ease-out;
            z-index: 1;
            margin-right: 5px;
        }

        .navigation__btn:last-child {
            margin-right: 0;
        }

        .navigation__btn:hover {
            color: gray;
        }

        .navigation__btn--active {
            pointer-events: none;
        }
    </style>

</head>
<body>
    <h1>Multi-item button slider</h1>

    <div class="navigation js-btn-slide-container">
        <span class="navigation__active-state js-btn-slide-active" role="presentation"></span>
        <a class="navigation__btn navigation__btn--active js-btn-slide" role="button">Option 1</a>
        <a class="navigation__btn js-btn-slide" role="button">Opt 2</a>
        <a class="navigation__btn js-btn-slide" role="button">Option 3</a>
        <a class="navigation__btn js-btn-slide" role="button">Option 3 Again</a>
        <a class="navigation__btn js-btn-slide" role="button">Option 5</a>
        <a class="navigation__btn js-btn-slide" role="button">Option 6 with a longer title</a>
    </div>

    <script>
        const buttonSlide = (() => {
            const activeState = (item, btnItem) => {
                const btnActive = item.getElementsByClassName('js-btn-slide-active')[0];
                let itemBounding = item.getBoundingClientRect();
                let btnBounding = btnItem.getBoundingClientRect();

                btnActive.style.opacity = 1;
                btnActive.style.left = Math.round(btnBounding.left) - Math.round(itemBounding.left) + 'px';
                btnActive.style.width = btnItem.offsetWidth + 'px';
            };
            const bindComponent = (item) => {
                const btn = item.getElementsByClassName('js-btn-slide');

                [...btn].forEach((btnItem) => {

                    if (btnItem.classList.contains('navigation__btn--active')) {
                        activeState(item, btnItem);
                    }
                    btnItem.addEventListener('click', () => {
                        for (let i = 0; i < btn.length; i++) {
                            btn[i].classList.remove('navigation__btn--active');
                        }
                        btnItem.classList.add('navigation__btn--active');
                        activeState(item, btnItem);
                    });

                    window.addEventListener('resize', () => activeState(item, btnItem));
                });
            };

            const init = () => {
                const rootEl = document.getElementsByClassName("js-btn-slide-container");
                [...rootEl].forEach((item) => bindComponent(item));
            };

            return {
                init
            };
        })();

        buttonSlide.init();
    </script>
    
</body>
</html>
