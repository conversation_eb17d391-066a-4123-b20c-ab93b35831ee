
html {
    height: 100%;
    margin: 0;
    overflow: hidden;
}




body {
height: 100%;   
  /* display: flex; */
  justify-content: start;
  color: #212529;
  background-color: #e0f7fa; /* Light icy color */
  /* font-family: 'Montserrat', sans-serif; */
  font-family:Arial, sans-serif;
  box-shadow: inset 0 0 2rem rgba(0, 0, 0, .5);
  margin: 0;
  overflow: hidden;
}
canvas { 
    display: block; 
}




#background {
    /* position: absolute; */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1; /* Behind content but above the body */
}


.doorFace.selected {
  border: 2px solid yellow; /* Visual indication of selection */
}


.doorFace.active {
  transform: scale(1.2); /* Scale up the panel */
  z-index: 100; /* Bring it to the top */
  opacity: 1; /* Ensure it's fully opaque */
  /* Any additional styling to highlight it */
}




  #doors {
    position: relative;
    width: 250px;
    height: 200px;
    left: 40%;
    top: 30px;
    z-index: 5; 
    /* background-color: #1680c2; */
  }
  .doorWrapper {
    width: 120px;
    height: 200px;
    position: absolute;
    float: left;
    margin: 0px;
    padding: 0px;
    cursor: pointer;
    -webkit-font-smoothing: antialiased;
  }
  .doorFace {
    position: absolute;
    width: 120px;
    height: 200px;
    overflow: hidden;
    text-align: center;
    transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
  }


/* Fullscreen state */
.doorFace.fullscreen {
  position: fixed; /* Or absolute, depending on your layout */
  top: 0;
  left: 0;
  width: 100vw; /* Full viewport width */
  height: 100vh; /* Full viewport height */
  transform: scale(1); /* Adjust scale as needed */
  z-index: 100; /* Ensure it's above other content */
  opacity: 1;
  /* Additional styles for fullscreen mode */
}

  .doorFace div {
    width: 100%;
    height: 100%;
    padding-top: 15%;
    float: left;
    font-size: 40px;
  }
  .in {
    background-color: #ffffff;
    color: #b9d1df;
  }
  .out {
    background-color: #b9d1df;
    color: #ffffff;
  }
  .d1{z-index: 10}
  .d2{z-index: 20}
  .d3{z-index: 30}
  .d4{z-index: 40}
  


  /* background stuff */

  header {
        flex: 0 0 auto; 
        position: relative; /* or 'absolute' depending on desired effect */
        z-index: 10; /* Ensures the header stays on top */
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.5); 
        backdrop-filter: blur(10px);
        padding: 10px 40px;
    }



  

#logo {
    font-size: 24px;
    font-weight: bold;
    width: 50px;
    height: auto;
    margin-right: 20px;
}

#navigation {
    display: flex;
    gap: 20px;
}

#navigation a {
    text-decoration: none;
    color: black;
    font-size: 20px;
}

#user-icon {
    font-size: 24px;
}
