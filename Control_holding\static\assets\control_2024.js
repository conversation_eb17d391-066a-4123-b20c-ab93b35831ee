document.addEventListener('DOMContentLoaded', function() {
    gsap.set(".doorWrapper", {perspective: 300});
    gsap.set(".door", {transformOrigin: "100% 50%", transformStyle: "preserve-3d"});
    gsap.set(".back", {rotationY: -180});
    gsap.set([".back", ".front"], {backfaceVisibility: "hidden"});
    gsap.set(".d2", {rotationY: -90});
    gsap.set(".d3", {rotationY: -180});
    gsap.set(".d4", {rotationY: -270});
  
    // Revolving door animation
    let doorAnimation = gsap.timeline({ repeat: -1 })
      .to(".door", { duration: 10, rotationY: "-=90", ease: "none" });
  
    // Function to align panels horizontally and reset rotation
    function alignPanelsHorizontally() {
      const doorWrappers = gsap.utils.toArray('.doorWrapper');
      doorWrappers.forEach((wrapper, index) => {
        gsap.to(wrapper, {
          duration: 1,
          x: index * wrapper.offsetWidth, // Adjust the position based on the index
          rotationY: 0, // Reset rotation
          transformOrigin: "center center",
          ease: "power3.inOut",
          onComplete: function() {
            wrapper.children[0].style.transform = 'none'; // Reset inline transform
          }
        });
      });
    }
  
    // Function to transition to the carousel view
    function transitionToCarousel() {
      const doorWrappers = gsap.utils.toArray('.doorWrapper');
      const activeIndex = doorWrappers.findIndex(d => d.querySelector('.doorFace').classList.contains('selected'));
      const offset = 100; // Adjust the space between the cards
  
      doorWrappers.forEach((wrapper, index) => {
        let position = (index - activeIndex) * offset;
  
        // Adjust zIndex based on active card
        let zIndex = index === activeIndex ? 100 : 1;
        gsap.to(wrapper, {
          duration: 1,
          x: position,
          rotationY: 0, // Reset any 3D rotation
          scale: index === activeIndex ? 1 : 0.8, // Scale based on active card
          ease: "power3.inOut",
          zIndex: zIndex,
          onComplete: () => {
            if (index === activeIndex) {
              alignPanelsHorizontally(); // Reset rotations after aligning horizontally
            }
          }
        });
      });
    }
  
    // Click event listener for door panels
    document.querySelectorAll('.doorFace').forEach(face => {
      face.addEventListener('click', function() {
        if (this.classList.contains('selected')) {
            console.log(this)
          // If already selected, trigger fullscreen
        //   this.classList.add('fullscreen');
      loadHTMLContent(this.children[0].getAttribute('data-destination'));
        //   loadHTMLContent(this.dataset.destination);
        } else {
          // Otherwise, select the new card and trigger the carousel
          document.querySelectorAll('.doorFace').forEach(df => df.classList.remove('selected'));
          this.classList.add('selected');
          doorAnimation.pause(); // Stop the revolving animation
          transitionToCarousel();
        }
      });
    });
  
    // Function to load HTML content
    function loadHTMLContent(destination) {
        if (!destination) {
            console.error('No destination provided');
            return;
          }
        // Redirect to the URL provided by the server for the clicked pane.
        // This assumes the server sends back a full path to redirect to.
        fetch('/get_page_content?destination=' + destination)
          .then(response => response.json())
          .then(data => {
            if (data.tab !== 'error') {
              window.location.href = data.tab; // Redirect to the new page.
            } else {
              console.error('Error retrieving page content.');
            }
          })
          .catch(err => console.error('Error loading page content:', err));
      }
      

    function expandToFullscreen(element) {
        // You need to set the zIndex high enough to ensure it covers other elements.
        gsap.to(element, {
          duration: 0.5, // Duration of the animation
          left: "0px", // Position to the far left of the viewport
          top: "0px", // Position to the top of the viewport
          width: "100vw", // Full width of the viewport
          height: "100vh", // Full height of the viewport
          position: "fixed", // Fixed position to stay in place when scrolling
          zIndex: 9999, // High zIndex to ensure it's on top
          ease: "power3.inOut", // Smoother transition for the animation
          onComplete: () => {
            // Now that the panel is expanded, you can load the content.
            // 
            loadHTMLContent('/get_page_content');
          }
        });
      }
      


  });



  