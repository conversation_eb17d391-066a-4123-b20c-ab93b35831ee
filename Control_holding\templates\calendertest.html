<!DOCTYPE html>
<html>
<head>
  <title>Radar Chart for Skills</title>
  <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body>
  <svg id="skillRadar" width="500" height="500"></svg>
  <script>
    const width = 500, height = 500;
    const margin = 50;
    const radius = Math.min(width, height) / 2 - margin;

    // Example data
    const skillData = [
      { skill_name: "Public Speaking", skill_type: "soft", level: 4 },
      { skill_name: "Tech Writing", skill_type: "soft", level: 3 },
      { skill_name: "C++ Programming", skill_type: "hard", level: 5 },
      { skill_name: "Field Work", skill_type: "hard", level: 4 }
    ];

    // We need an axis for each skill_name
    const axisNames = skillData.map(d => d.skill_name);
    const levels = skillData.map(d => d.level);
    const maxLevel = d3.max(levels);

    const svg = d3.select("#skillRadar")
                  .attr("width", width)
                  .attr("height", height);

    const g = svg.append("g")
                 .attr("transform", `translate(${width/2}, ${height/2})`);

    const angleSlice = (2 * Math.PI) / axisNames.length;

    // Plot lines for each axis
    axisNames.forEach((name, i) => {
      const angle = i * angleSlice - Math.PI/2;
      g.append("line")
       .attr("x1", 0)
       .attr("y1", 0)
       .attr("x2", radius * Math.cos(angle))
       .attr("y2", radius * Math.sin(angle))
       .attr("stroke", "gray");
      g.append("text")
       .attr("x", (radius + 10) * Math.cos(angle))
       .attr("y", (radius + 10) * Math.sin(angle))
       .attr("text-anchor", "middle")
       .text(name);
    });

    // Convert skill levels to radial coordinates
    const lineGenerator = d3.lineRadial()
                            .radius(d => (d.level / maxLevel) * radius)
                            .angle((d, i) => i * angleSlice)
                            .curve(d3.curveLinearClosed);

    // We just have one "set" of data for this staff
    // Map skillData to an array of objects: { skill_name, level }
    // preserving the index for the angle
    const radialData = skillData.map((d, i) => ({ ...d, i }));

    g.append("path")
      .datum(radialData)
      .attr("fill", "rgba(0,128,255,0.3)")
      .attr("stroke", "#007bff")
      .attr("stroke-width", 2)
      .attr("d", lineGenerator);
    
    // Circles at data points
    g.selectAll(".radarCircle")
      .data(radialData)
      .enter()
      .append("circle")
      .attr("class", "radarCircle")
      .attr("r", 4)
      .attr("cx", (d, i) => (d.level / maxLevel) * radius * Math.cos(i * angleSlice - Math.PI/2))
      .attr("cy", (d, i) => (d.level / maxLevel) * radius * Math.sin(i * angleSlice - Math.PI/2))
      .attr("fill", "#007bff");
  </script>
</body>
</html>
