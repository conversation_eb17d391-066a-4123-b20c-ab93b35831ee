
function App(conf) {
    // Default configuration
    const defaultConf = {
      fov:35,
      cameraZ: 75,
      xyCoef: 50,
      zCoef: 6,
      lightIntensity: 1.2,
      ambientColor: 0x000000,
      light1Color: 0x0E09DC,
      light2Color: 0x1CD1E1,
      light3Color: 0x18C02C,
      light4Color: 0xee3bcf
    };
    conf = Object.assign(defaultConf, conf);
  
    let renderer, scene, camera;
    let width, height, wWidth, wHeight;
    let plane;
    const simplex = new SimplexNoise();
  
    const mouse = new THREE.Vector2();
    const mousePlane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0);
    const mousePosition = new THREE.Vector3();
    const raycaster = new THREE.Raycaster();
  
    function init() {
      renderer = new THREE.WebGLRenderer({ canvas: document.getElementById(conf.el), antialias: true, alpha: true });
      camera = new THREE.PerspectiveCamera(conf.fov, window.innerWidth / window.innerHeight, 0.1, 1000);
    //   camera.position.z = conf.cameraZ;
      camera.position.z = 60; // Adjust camera position
    camera.lookAt(new THREE.Vector3(0, 0, 0)); // Make the camera look at the center of the scene

  
      updateSize();
      window.addEventListener('resize', updateSize, false);
  
      document.addEventListener('mousemove', e => {
        const v = new THREE.Vector3();
        camera.getWorldDirection(v);
        v.normalize();
        mousePlane.normal = v;
        mouse.x = (e.clientX / width) * 2 - 1;
        mouse.y = - (e.clientY / height) * 2 + 1;
        raycaster.setFromCamera(mouse, camera);
        raycaster.ray.intersectPlane(mousePlane, mousePosition);
      });
  
      initScene();
      animate();
    }
  
    function initScene() {
      scene = new THREE.Scene();
      initLights();
  
      let mat = new THREE.MeshLambertMaterial({ color: 0xffffff, side: THREE.DoubleSide });
    //   let mat = new THREE.MeshPhongMaterial({
    //     color: 0xffffff,
    //     specular: 0xaaaaaa,
    //     shininess: 50,
    //     side: THREE.DoubleSide
    // });
    

      let geo = new THREE.PlaneBufferGeometry(wWidth, wHeight, wWidth / 2, wHeight / 2);
      plane = new THREE.Mesh(geo, mat);
      scene.add(plane);
  
      plane.rotation.x = -Math.PI / 2 - 0.2;
      plane.position.y = -25;
      camera.position.z = 60;
    }
  
    function initLights() {
      const r = 30;
      const y = 10;
      const lightDistance = 500;
  
      let light1 = new THREE.PointLight(conf.light1Color, conf.lightIntensity, lightDistance);
      light1.position.set(0, y, r);
      scene.add(light1);
  
      let light2 = new THREE.PointLight(conf.light2Color, conf.lightIntensity, lightDistance);
      light2.position.set(0, -y, -r);
      scene.add(light2);
  
      let light3 = new THREE.PointLight(conf.light3Color, conf.lightIntensity, lightDistance);

      light3.position.set(r, y, 0);
      scene.add(light3);
  
      let light4 = new THREE.PointLight(conf.light4Color, conf.lightIntensity, lightDistance);
      light4.position.set(-r, y, 0);
      scene.add(light4);

      light1.name = "light1";
      light2.name = "light2";
        light3.name = "light3";
        light4.name = "light4";

    // Add ambient light
    let ambientLight = new THREE.AmbientLight(0xffffff, 0.19); // Adjust color and intensity as needed
    scene.add(ambientLight);

    }

  
    function animate() {
      requestAnimationFrame(animate);
  
      animatePlane();
      animateLights();
  
      renderer.render(scene, camera);
    }
  
    function animatePlane() {
      conf.mouseSensitivity = 0.1; // Adjust this value to control sensitivity

      let gArray = plane.geometry.attributes.position.array;
      const time = Date.now() * 0.0002;
      for (let i = 0; i < gArray.length; i += 3) {
        gArray[i + 2] = simplex.noise4D(gArray[i] / conf.xyCoef, gArray[i + 1] / conf.xyCoef, time, (mouse.x + mouse.y) * conf.mouseSensitivity) * conf.zCoef;

        // gArray[i + 2] = simplex.noise4D(gArray[i] / conf.xyCoef, gArray[i + 1] / conf.xyCoef, time, mouse.x + mouse.y) * conf.zCoef;
      }
      plane.geometry.attributes.position.needsUpdate = true;
    }
  
    function animateLights() {
      const time = Date.now() * 0.001;
      const d = 50;
      let light1 = scene.getObjectByName("light1");
      let light2 = scene.getObjectByName("light2");
      let light3 = scene.getObjectByName("light3");
      let light4 = scene.getObjectByName("light4");
  
      light1.position.x = Math.sin(time * 0.1) * d;
      light1.position.z = Math.cos(time * 0.2) * d;
      light2.position.x = Math.cos(time * 0.3) * d;
      light2.position.z = Math.sin(time * 0.4) * d;
      light3.position.x = Math.sin(time * 0.5) * d;
      light3.position.z = Math.sin(time * 0.6) * d;
      light4.position.x = Math.sin(time * 0.7) * d;
      light4.position.z = Math.cos(time * 0.8) * d;
    }
  
    function updateSize() {
      width = window.innerWidth;
      height = window.innerHeight;
      renderer.setSize(width, height);
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      wWidth = width;
      wHeight = height;
    }
  
    init();
  }
  
  // Initialize the app
  App({ el: 'background' });
  